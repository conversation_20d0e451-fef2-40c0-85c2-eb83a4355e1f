# OpenAI RAG Pipeline Workflow Flowchart

## Complete Power Plant Data Extraction Pipeline

```mermaid
flowchart TD
    A[🚀 START: OpenAI RAG Pipeline] --> B[⚙️ Initialize Components]
    
    B --> B1[🧠 OpenAI Client<br/>gpt-4o-mini]
    B --> B2[🔍 SERP API Client<br/>Google Search]
    B --> B3[🌐 ScraperAPI Client<br/>Web Scraping]
    B --> B4[📊 Enhanced Extractor<br/>Adaptive LLM]
    B --> B5[🔧 Plant Details Extractor<br/>Technical Data]
    
    B1 --> C
    B2 --> C
    B3 --> C
    B4 --> C
    B5 --> C[✅ Initialization Complete]
    
    C --> D[🔍 STEP 1: Search Phase]
    D --> D1[📡 Query: 'Jhajjar Power Plant']
    D1 --> D2[🔍 SERP API Search<br/>Top 5 Results]
    D2 --> D3{Search Success?}
    
    D3 -->|❌ No| D4[⚠️ Search Failed<br/>Use Fallback]
    D3 -->|✅ Yes| E[🌐 STEP 2: Scraping Phase]
    
    E --> E1[📄 Scrape URL 1/5<br/>async with scraper_client]
    E1 --> E2[📄 Scrape URL 2/5]
    E2 --> E3[📄 Scrape URL 3/5]
    E3 --> E4[📄 Scrape URL 4/5]
    E4 --> E5[📄 Scrape URL 5/5]
    
    E5 --> E6{All Scraped?}
    E6 -->|❌ Some Failed| E7[⚠️ Continue with<br/>Available Content]
    E6 -->|✅ Success| F[📊 STEP 3: Organizational Extraction]
    E7 --> F
    
    F --> F1[🧠 AdaptiveExtractor.extract_adaptively]
    F1 --> F2[📝 Content Analysis<br/>5 sources, 13,541 chars]
    F2 --> F3[🎯 Field-by-Field LLM Extraction]
    
    F3 --> F4[🏢 organization_name<br/>✅ CLP India Private Limited]
    F4 --> F5[🌍 country_name<br/>✅ India]
    F5 --> F6[📍 province<br/>✅ Haryana]
    F6 --> F7[⚡ plant_types<br/>✅ coal]
    F7 --> F8[🏭 plants_count<br/>✅ 1]
    F8 --> F9[💰 currency_in<br/>✅ INR]
    F9 --> F10[📅 financial_year<br/>✅ 04-03]
    
    F10 --> G[🔧 STEP 4: Plant Details Extraction]
    
    G --> G1[🧠 Analyze Cached Content<br/>PlantDetailsExtractor]
    G1 --> G2[🎯 Extract from Cache]
    
    G2 --> G3[📛 name: ✅ From org_name]
    G3 --> G4[⚙️ plant_type: ✅ From org data]
    G4 --> G5[❌ lat: Missing]
    G5 --> G6[❌ long: Missing]
    G6 --> G7[❌ plant_address: Missing]
    G7 --> G8[❌ units_id: Missing]
    G8 --> G9[❌ grid_connectivity: Missing]
    G9 --> G10[❌ ppa_details: Missing]
    
    G10 --> H[🎯 STEP 5: RAG Targeted Searches]
    
    H --> H1[🔍 RAG Search: Latitude<br/>Query: 'Jhajjar Power Plant latitude coordinate']
    H1 --> H2[📄 Scrape 3 Targeted URLs]
    H2 --> H3[🧠 RAG Pattern Matching<br/>Regex: ([0-9]+\.[0-9]+)]
    H3 --> H4[✅ Found: 28.607111]
    
    H4 --> I1[🔍 RAG Search: Longitude<br/>Query: 'Jhajjar Power Plant longitude coordinate']
    I1 --> I2[📄 Scrape 3 Targeted URLs]
    I2 --> I3[🧠 RAG Pattern Matching<br/>Regex: [0-9]+\.[0-9]+[,\s]*([0-9]+\.[0-9]+)]
    I3 --> I4[✅ Found: 76.656914]
    
    I4 --> J1[🔍 RAG Search: Address<br/>Query: 'Jhajjar Power Plant district city location']
    J1 --> J2[📄 Scrape 3 Targeted URLs]
    J2 --> J3[🧠 RAG Text Extraction]
    J3 --> J4[✅ Found: Jharli village, Jhajjar district, Haryana]
    
    J4 --> K1[🔍 RAG Search: Grid Connectivity<br/>Query: 'Jhajjar Power Plant grid substation transmission']
    K1 --> K2[📄 Scrape PDFs + Web Pages]
    K2 --> K3[🧠 RAG JSON Parsing<br/>Pydantic Models]
    K3 --> K4[✅ Found: Transmission substations]
    
    K4 --> L1[🔍 RAG Search: PPA Details<br/>Query: 'Jhajjar Power Plant power purchase agreement']
    L1 --> L2[📄 Scrape Regulatory PDFs]
    L2 --> L3[🧠 RAG Structured Extraction]
    L3 --> L4[✅ Found: PPA structure with respondents]
    
    L4 --> M1[🔍 RAG Search: Units<br/>Query: 'Jhajjar Power Plant generation units number']
    M1 --> M2[📄 Scrape Technical Documents]
    M2 --> M3[🧠 RAG Number Extraction]
    M3 --> M4[✅ Found: [1, 2]]
    
    M4 --> N[💾 STEP 6: Data Consolidation]
    
    N --> N1[📊 Create OrganizationalDetails JSON<br/>8/9 fields (89% complete)]
    N1 --> N2[🔧 Create PlantDetails JSON<br/>9/9 fields (100% complete)]
    N2 --> N3[📈 Create ExtractionInfo JSON<br/>Metrics & Performance]
    
    N3 --> O[💾 STEP 7: File Output]
    
    O --> O1[📊 jhajjar_org_openai_rag_TIMESTAMP.json]
    O --> O2[🔧 jhajjar_plant_openai_rag_TIMESTAMP.json]
    O --> O3[📈 jhajjar_extraction_info_openai_rag_TIMESTAMP.json]
    
    O1 --> P[📄 STEP 8: Clean JSON Display]
    O2 --> P
    O3 --> P
    
    P --> P1[🖥️ Print Organizational JSON]
    P1 --> P2[🖥️ Print Plant Technical JSON]
    P2 --> Q[✅ PIPELINE COMPLETE]
    
    Q --> Q1[⏱️ Total Time: 405.9s]
    Q1 --> Q2[📊 Cache Efficiency: 2 fields]
    Q2 --> Q3[🎯 RAG Extractions: 6 fields]
    Q3 --> Q4[🎉 SUCCESS: 100% Plant Data Extracted]
    
    %% Error Handling Paths
    D4 --> R[🔄 Retry with Different Query]
    R --> D1
    
    %% Styling
    classDef startEnd fill:#e1f5fe,stroke:#01579b,stroke-width:3px
    classDef process fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef success fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef error fill:#ffebee,stroke:#c62828,stroke-width:2px
    classDef rag fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
    
    class A,Q4 startEnd
    class B1,B2,B3,B4,B5,F1,G1,N1,N2,N3 process
    class F4,F5,F6,F7,F8,F9,F10,G3,G4,H4,I4,J4,K4,L4,M4 success
    class G5,G6,G7,G8,G9,G10 error
    class H1,H2,H3,I1,I2,I3,J1,J2,J3,K1,K2,K3,L1,L2,L3,M1,M2,M3 rag
```

## Key Pipeline Components

### 🔧 **Technical Architecture**
- **LLM**: OpenAI GPT-4o-mini
- **Search**: SERP API (Google Search)
- **Scraping**: ScraperAPI with async context managers
- **Extraction**: Adaptive LLM + RAG pattern matching
- **Output**: Structured JSON with Pydantic validation

### 📊 **Data Flow Strategy**
1. **Initial Broad Search** → Top 5 most relevant URLs
2. **Comprehensive Scraping** → Extract all available content
3. **LLM Organizational Extraction** → High-level company data
4. **Cache Analysis** → Identify missing technical fields
5. **Targeted RAG Searches** → Field-specific queries
6. **Pattern-Based Extraction** → Regex + LLM hybrid approach
7. **Structured Output** → Clean JSON with metadata

### 🎯 **RAG Enhancement Benefits**
- **Precision**: Targeted searches for missing fields
- **Efficiency**: Avoid redundant API calls
- **Accuracy**: Pattern matching + LLM validation
- **Completeness**: 100% field extraction success
- **Performance**: Cache-first approach reduces latency

### ⚡ **Performance Metrics**
- **Total Time**: ~6.8 minutes
- **Success Rate**: 100% for plant details, 89% for organizational
- **Cache Efficiency**: 25% fields from cache
- **RAG Success**: 75% fields via targeted extraction
