2025-05-28 12:11:04,519 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 12:11:04,519 - src.enhanced_extractor - INFO - Enhanced data extractor initialized with OpenAI
2025-05-28 12:11:04,525 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 12:11:04,535 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 12:11:04,535 - src.plant_details_extractor - INFO - Plant details extractor initialized with OpenAI
2025-05-28 12:11:04,535 - src.simple_pipeline - INFO - 🔍 Starting simplified extraction for: Jhajjar Power Plant
2025-05-28 12:11:04,535 - src.simple_pipeline - INFO - 📡 Step 1: Searching for 'Jhajjar Power Plant' - getting top 5 links
2025-05-28 12:11:22,577 - src.simple_pipeline - INFO - 🌐 Step 2: Scraping 5 top results
2025-05-28 12:11:22,578 - src.simple_pipeline - INFO - Scraping 1/5: https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html
2025-05-28 12:11:23,699 - src.simple_pipeline - INFO - Successfully scraped https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html (516 chars)
2025-05-28 12:11:24,700 - src.simple_pipeline - INFO - Scraping 2/5: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 12:11:25,388 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-28 12:11:26,390 - src.simple_pipeline - INFO - Scraping 3/5: https://apraava.com/projects/jhajjar-power-plant
2025-05-28 12:11:27,872 - src.simple_pipeline - INFO - Successfully scraped https://apraava.com/projects/jhajjar-power-plant (791 chars)
2025-05-28 12:11:28,874 - src.simple_pipeline - INFO - Scraping 4/5: https://m.economictimes.com/industry/energy/power/jindal-power-emerges-frontrunner-to-acquire-apraavas-haryana-project/articleshow/121345946.cms
2025-05-28 12:11:30,149 - src.simple_pipeline - INFO - Successfully scraped https://m.economictimes.com/industry/energy/power/jindal-power-emerges-frontrunner-to-acquire-apraavas-haryana-project/articleshow/121345946.cms (2246 chars)
2025-05-28 12:11:31,151 - src.simple_pipeline - INFO - Scraping 5/5: https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr
2025-05-28 12:11:33,102 - src.simple_pipeline - INFO - Successfully scraped https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr (3864 chars)
2025-05-28 12:11:34,105 - src.simple_pipeline - INFO - ✅ Initial scraping completed: 5 pages scraped
2025-05-28 12:11:34,105 - src.simple_pipeline - INFO - 📊 Step 3: Extracting organizational details from scraped content
2025-05-28 12:11:34,106 - src.enhanced_extractor - INFO - Content analysis: {'total_sources': 5, 'total_content': 7907, 'avg_relevance': 0.8380000000000001, 'high_quality_sources': 1, 'source_type_diversity': 3, 'source_types': ['company_official', 'other', 'wikipedia']}
2025-05-28 12:11:34,106 - src.enhanced_extractor - INFO - Using basic extraction with validation
2025-05-28 12:11:34,106 - src.openai_client - INFO - Starting OpenAI LLM extraction for 5 content pieces
2025-05-28 12:11:35,111 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 12:11:35,119 - src.openai_client - INFO - Extracted organization_name: CLP India Private Limited (confidence: 0.90)
2025-05-28 12:11:36,622 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 12:11:36,626 - src.openai_client - INFO - Extracted cfpp_type: joint_venture (confidence: 0.60)
2025-05-28 12:11:37,923 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 12:11:37,927 - src.openai_client - INFO - Extracted country_name: India (confidence: 0.90)
2025-05-28 12:11:39,214 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 12:11:39,215 - src.openai_client - INFO - Extracted province: Haryana (confidence: 0.90)
2025-05-28 12:18:03,953 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 12:18:03,953 - src.enhanced_extractor - INFO - Enhanced data extractor initialized with OpenAI
2025-05-28 12:18:03,958 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 12:18:03,970 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 12:18:03,970 - src.plant_details_extractor - INFO - Plant details extractor initialized with OpenAI
2025-05-28 12:18:03,970 - src.simple_pipeline - INFO - 🔍 Starting simplified extraction for: Jhajjar Power Plant
2025-05-28 12:18:03,970 - src.simple_pipeline - INFO - 📡 Step 1: Searching for 'Jhajjar Power Plant' - getting top 5 links
2025-05-28 12:18:25,095 - src.simple_pipeline - INFO - 🌐 Step 2: Scraping 5 top results
2025-05-28 12:18:25,097 - src.simple_pipeline - INFO - Scraping 1/5: https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html
2025-05-28 12:18:28,703 - src.simple_pipeline - INFO - Successfully scraped https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html (516 chars)
2025-05-28 12:18:29,705 - src.simple_pipeline - INFO - Scraping 2/5: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 12:18:30,445 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-28 12:18:31,447 - src.simple_pipeline - INFO - Scraping 3/5: https://apraava.com/projects/jhajjar-power-plant
2025-05-28 12:18:37,872 - src.simple_pipeline - INFO - Successfully scraped https://apraava.com/projects/jhajjar-power-plant (791 chars)
2025-05-28 12:18:38,874 - src.simple_pipeline - INFO - Scraping 4/5: https://m.economictimes.com/industry/energy/power/jindal-power-emerges-frontrunner-to-acquire-apraavas-haryana-project/articleshow/121345946.cms
2025-05-28 12:18:44,937 - src.simple_pipeline - INFO - Successfully scraped https://m.economictimes.com/industry/energy/power/jindal-power-emerges-frontrunner-to-acquire-apraavas-haryana-project/articleshow/121345946.cms (2246 chars)
2025-05-28 12:18:45,937 - src.simple_pipeline - INFO - Scraping 5/5: https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr
2025-05-28 12:18:47,408 - src.simple_pipeline - INFO - Successfully scraped https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr (3864 chars)
2025-05-28 12:18:48,409 - src.simple_pipeline - INFO - ✅ Initial scraping completed: 5 pages scraped
2025-05-28 12:18:48,409 - src.simple_pipeline - INFO - 📊 Step 3: Extracting organizational details from scraped content
2025-05-28 12:18:48,409 - src.enhanced_extractor - INFO - Content analysis: {'total_sources': 5, 'total_content': 7907, 'avg_relevance': 0.8380000000000001, 'high_quality_sources': 1, 'source_type_diversity': 3, 'source_types': ['wikipedia', 'other', 'company_official']}
2025-05-28 12:18:48,409 - src.enhanced_extractor - INFO - Using basic extraction with validation
2025-05-28 12:18:48,409 - src.openai_client - INFO - Starting OpenAI LLM extraction for 5 content pieces
2025-05-28 12:18:49,192 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 12:18:49,199 - src.openai_client - INFO - Extracted organization_name: CLP India Private Limited (confidence: 0.90)
2025-05-28 12:18:50,299 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 12:18:50,306 - src.openai_client - INFO - Extracted cfpp_type: joint_venture (confidence: 0.60)
2025-05-28 12:18:51,660 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 12:18:51,663 - src.openai_client - INFO - Extracted country_name: India (confidence: 0.90)
2025-05-28 12:18:52,949 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 12:18:52,951 - src.openai_client - INFO - Extracted province: Haryana (confidence: 0.90)
2025-05-28 12:18:54,494 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 12:18:54,497 - src.openai_client - INFO - Extracted plants_count: None (confidence: 0.00)
2025-05-28 12:18:55,758 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 12:18:55,762 - src.openai_client - INFO - Extracted plant_types: ['coal'] (confidence: 0.80)
2025-05-28 12:18:56,974 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 12:18:56,976 - src.openai_client - INFO - Extracted ppa_flag:  (confidence: 0.00)
2025-05-28 12:18:58,084 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 12:18:58,089 - src.openai_client - INFO - Extracted currency_in: INR (confidence: 0.70)
2025-05-28 12:18:59,204 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 12:18:59,209 - src.openai_client - INFO - Extracted financial_year: 04-03 (confidence: 0.70)
2025-05-28 12:18:59,711 - src.simple_pipeline - INFO - ✅ Organizational extraction completed
2025-05-28 12:18:59,711 - src.simple_pipeline - INFO - 🔧 Step 4: Extracting plant details with smart field-by-field approach
2025-05-28 12:18:59,712 - src.simple_pipeline - INFO - 🧠 Analyzing cached content for plant details extraction
2025-05-28 12:18:59,712 - src.plant_details_extractor - INFO - Starting plant details extraction for: Jhajjar Power Plant
2025-05-28 12:19:07,716 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field name: RetryError[<Future at 0x107328860 state=finished raised TypeError>]
2025-05-28 12:19:07,716 - src.plant_details_extractor - INFO - Extracted name: None (confidence: 0.00)
2025-05-28 12:19:16,220 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field plant_type: RetryError[<Future at 0x1075b0560 state=finished raised TypeError>]
2025-05-28 12:19:16,222 - src.plant_details_extractor - INFO - Extracted plant_type: None (confidence: 0.00)
2025-05-28 12:19:24,726 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field plant_address: RetryError[<Future at 0x107579c40 state=finished raised TypeError>]
2025-05-28 12:19:24,727 - src.plant_details_extractor - INFO - Extracted plant_address: None (confidence: 0.00)
2025-05-28 12:19:33,235 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field lat: RetryError[<Future at 0x1075efef0 state=finished raised TypeError>]
2025-05-28 12:19:33,238 - src.plant_details_extractor - INFO - Extracted lat: None (confidence: 0.00)
2025-05-28 12:19:41,744 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field long: RetryError[<Future at 0x1075478f0 state=finished raised TypeError>]
2025-05-28 12:19:41,745 - src.plant_details_extractor - INFO - Extracted long: None (confidence: 0.00)
2025-05-28 12:19:50,250 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field units_id: RetryError[<Future at 0x1076024b0 state=finished raised TypeError>]
2025-05-28 12:19:50,251 - src.plant_details_extractor - INFO - Extracted units_id: None (confidence: 0.00)
2025-05-28 12:19:50,753 - src.plant_details_extractor - ERROR - Failed to extract grid_connectivity: No prompt template found for plant field: grid_connectivity
2025-05-28 12:19:50,753 - src.plant_details_extractor - ERROR - Failed to extract ppa_details: '\n  "capacity"'
2025-05-28 12:19:50,754 - src.plant_details_extractor - INFO - Using organization name for plant name: CLP India Private Limited
2025-05-28 12:19:50,754 - src.plant_details_extractor - INFO - Derived plant_type from org data: coal
2025-05-28 12:19:50,754 - src.plant_details_extractor - INFO - Plant details extraction completed for: Jhajjar Power Plant
2025-05-28 12:19:50,754 - src.simple_pipeline - INFO - ✅ Field 'name' extracted from cache
2025-05-28 12:19:50,754 - src.simple_pipeline - INFO - ✅ Field 'plant_type' extracted from cache
2025-05-28 12:19:50,754 - src.simple_pipeline - INFO - ✅ Field 'plant_id' extracted from cache
2025-05-28 12:19:50,754 - src.simple_pipeline - INFO - 🔍 Field 'lat' is missing - will search specifically
2025-05-28 12:19:50,755 - src.simple_pipeline - INFO - 🔍 Field 'long' is missing - will search specifically
2025-05-28 12:19:50,755 - src.simple_pipeline - INFO - 🔍 Field 'plant_address' is missing - will search specifically
2025-05-28 12:19:50,755 - src.simple_pipeline - INFO - 🔍 Field 'grid_connectivity_maps' is missing - will search specifically
2025-05-28 12:19:50,755 - src.simple_pipeline - INFO - 🔍 Field 'ppa_details' is missing - will search specifically
2025-05-28 12:19:50,755 - src.simple_pipeline - INFO - 🔍 Field 'units_id' is missing - will search specifically
2025-05-28 12:19:50,755 - src.simple_pipeline - INFO - 🎯 Starting targeted searches for 6 missing fields
2025-05-28 12:19:50,755 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'lat' data
2025-05-28 12:19:50,755 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant plant's own latitude coordinate decimal degrees GPS location'
2025-05-28 12:19:58,732 - src.simple_pipeline - INFO - Scraping 1/3: https://globalenergyobservatory.org/geoid/42782
2025-05-28 12:20:00,738 - src.simple_pipeline - INFO - Successfully scraped https://globalenergyobservatory.org/geoid/42782 (106 chars)
2025-05-28 12:20:01,739 - src.simple_pipeline - INFO - Scraping 2/3: https://database.earth/countries/india/regions/haryana/cities/jhajjar
2025-05-28 12:20:02,694 - src.simple_pipeline - INFO - Successfully scraped https://database.earth/countries/india/regions/haryana/cities/jhajjar (685 chars)
2025-05-28 12:20:03,695 - src.simple_pipeline - INFO - Scraping 3/3: https://www.latlong.net/place/jhajjar-haryana-india-21959.html
2025-05-28 12:20:06,897 - src.simple_pipeline - INFO - Successfully scraped https://www.latlong.net/place/jhajjar-haryana-india-21959.html (1006 chars)
2025-05-28 12:20:07,899 - src.simple_pipeline - INFO - ✅ Found targeted content for 'lat' - 3 sources
2025-05-28 12:20:07,901 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'lat' using RAG: 28.607111
2025-05-28 12:20:07,901 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'long' data
2025-05-28 12:20:07,901 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant plant's own longitude coordinate decimal degrees GPS location'
2025-05-28 12:20:14,167 - src.simple_pipeline - INFO - Scraping 1/3: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 12:20:15,033 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-28 12:20:16,034 - src.simple_pipeline - INFO - Scraping 2/3: https://database.earth/countries/india/regions/haryana/cities/jhajjar
2025-05-28 12:20:18,134 - src.simple_pipeline - INFO - Successfully scraped https://database.earth/countries/india/regions/haryana/cities/jhajjar (685 chars)
2025-05-28 12:20:19,136 - src.simple_pipeline - INFO - Scraping 3/3: https://www.latlong.net/place/jhajjar-haryana-india-21959.html
2025-05-28 12:20:19,846 - src.simple_pipeline - INFO - Successfully scraped https://www.latlong.net/place/jhajjar-haryana-india-21959.html (1006 chars)
2025-05-28 12:20:20,849 - src.simple_pipeline - INFO - ✅ Found targeted content for 'long' - 3 sources
2025-05-28 12:20:20,851 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'long' using RAG: 76.6565
2025-05-28 12:20:20,851 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'plant_address' data
2025-05-28 12:20:20,851 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant district or city state country location'
2025-05-28 12:20:31,783 - src.simple_pipeline - INFO - Scraping 1/3: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 12:20:33,194 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-28 12:20:34,195 - src.simple_pipeline - INFO - Scraping 2/3: https://en.wikipedia.org/wiki/Jhajjar
2025-05-28 12:20:35,108 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar (6001 chars)
2025-05-28 12:20:36,110 - src.simple_pipeline - INFO - Scraping 3/3: https://apraava.com/projects/jhajjar-power-plant
2025-05-28 12:20:37,973 - src.simple_pipeline - INFO - Successfully scraped https://apraava.com/projects/jhajjar-power-plant (791 chars)
2025-05-28 12:20:38,975 - src.simple_pipeline - INFO - ✅ Found targeted content for 'plant_address' - 3 sources
2025-05-28 12:20:38,977 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'plant_address' using RAG: On The Road
2025-05-28 12:20:38,977 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'grid_connectivity_maps' data
2025-05-28 12:20:38,977 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant grid connection substation transmission line rated capacity classification voltage level'
