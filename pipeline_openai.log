2025-05-28 12:11:04,519 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 12:11:04,519 - src.enhanced_extractor - INFO - Enhanced data extractor initialized with OpenAI
2025-05-28 12:11:04,525 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 12:11:04,535 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 12:11:04,535 - src.plant_details_extractor - INFO - Plant details extractor initialized with OpenAI
2025-05-28 12:11:04,535 - src.simple_pipeline - INFO - 🔍 Starting simplified extraction for: Jhajjar Power Plant
2025-05-28 12:11:04,535 - src.simple_pipeline - INFO - 📡 Step 1: Searching for 'Jhajjar Power Plant' - getting top 5 links
2025-05-28 12:11:22,577 - src.simple_pipeline - INFO - 🌐 Step 2: Scraping 5 top results
2025-05-28 12:11:22,578 - src.simple_pipeline - INFO - Scraping 1/5: https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html
2025-05-28 12:11:23,699 - src.simple_pipeline - INFO - Successfully scraped https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html (516 chars)
2025-05-28 12:11:24,700 - src.simple_pipeline - INFO - Scraping 2/5: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 12:11:25,388 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-28 12:11:26,390 - src.simple_pipeline - INFO - Scraping 3/5: https://apraava.com/projects/jhajjar-power-plant
2025-05-28 12:11:27,872 - src.simple_pipeline - INFO - Successfully scraped https://apraava.com/projects/jhajjar-power-plant (791 chars)
2025-05-28 12:11:28,874 - src.simple_pipeline - INFO - Scraping 4/5: https://m.economictimes.com/industry/energy/power/jindal-power-emerges-frontrunner-to-acquire-apraavas-haryana-project/articleshow/121345946.cms
2025-05-28 12:11:30,149 - src.simple_pipeline - INFO - Successfully scraped https://m.economictimes.com/industry/energy/power/jindal-power-emerges-frontrunner-to-acquire-apraavas-haryana-project/articleshow/121345946.cms (2246 chars)
2025-05-28 12:11:31,151 - src.simple_pipeline - INFO - Scraping 5/5: https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr
2025-05-28 12:11:33,102 - src.simple_pipeline - INFO - Successfully scraped https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr (3864 chars)
2025-05-28 12:11:34,105 - src.simple_pipeline - INFO - ✅ Initial scraping completed: 5 pages scraped
2025-05-28 12:11:34,105 - src.simple_pipeline - INFO - 📊 Step 3: Extracting organizational details from scraped content
2025-05-28 12:11:34,106 - src.enhanced_extractor - INFO - Content analysis: {'total_sources': 5, 'total_content': 7907, 'avg_relevance': 0.8380000000000001, 'high_quality_sources': 1, 'source_type_diversity': 3, 'source_types': ['company_official', 'other', 'wikipedia']}
2025-05-28 12:11:34,106 - src.enhanced_extractor - INFO - Using basic extraction with validation
2025-05-28 12:11:34,106 - src.openai_client - INFO - Starting OpenAI LLM extraction for 5 content pieces
2025-05-28 12:11:35,111 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 12:11:35,119 - src.openai_client - INFO - Extracted organization_name: CLP India Private Limited (confidence: 0.90)
2025-05-28 12:11:36,622 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 12:11:36,626 - src.openai_client - INFO - Extracted cfpp_type: joint_venture (confidence: 0.60)
2025-05-28 12:11:37,923 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 12:11:37,927 - src.openai_client - INFO - Extracted country_name: India (confidence: 0.90)
2025-05-28 12:11:39,214 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 12:11:39,215 - src.openai_client - INFO - Extracted province: Haryana (confidence: 0.90)
