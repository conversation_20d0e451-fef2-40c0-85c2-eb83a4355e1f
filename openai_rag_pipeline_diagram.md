# OpenAI RAG Pipeline Working Diagram

## System Architecture & Data Flow Visualization

```mermaid
graph TB
    subgraph "🎯 INPUT LAYER"
        A[🏭 Target Plant<br/>Jhajjar Power Plant]
    end
    
    subgraph "⚙️ INITIALIZATION LAYER"
        B1[🧠 OpenAI Client<br/>gpt-4o-mini<br/>API Key: sk-...]
        B2[🔍 SERP API Client<br/>Google Search<br/>ScraperAPI Integration]
        B3[🌐 ScraperAPI Client<br/>Web Scraping<br/>Async Context Manager]
        B4[📊 Enhanced Extractor<br/>Adaptive LLM<br/>Field-by-Field Processing]
        B5[🔧 Plant Details Extractor<br/>Technical Data<br/>Pydantic Models]
    end
    
    subgraph "🔍 SEARCH & DISCOVERY LAYER"
        C1[📡 Initial Search Query<br/>'Jhajjar Power Plant']
        C2[🌐 Google SERP Results<br/>Top 5 URLs Retrieved]
        C3[📄 URL List<br/>• clpgroup.com<br/>• wikipedia.org<br/>• gem.wiki<br/>• apraava.com<br/>• apraava.com/newsroom]
    end
    
    subgraph "🌐 CONTENT EXTRACTION LAYER"
        D1[📄 Parallel Scraping<br/>async with scraper_client]
        D2[📊 Content Aggregation<br/>Total: 13,541 chars<br/>5 sources processed]
        D3[🎯 Content Quality Analysis<br/>Relevance: 83.8%<br/>High Quality: 1 source<br/>Source Diversity: 3 types]
    end
    
    subgraph "🧠 LLM EXTRACTION LAYER"
        E1[📊 Organizational Extraction<br/>AdaptiveExtractor.extract_adaptively]
        E2[🎯 Field-by-Field Processing<br/>Individual LLM calls per field]
        E3[✅ Extracted Fields<br/>• organization_name: CLP India<br/>• country: India<br/>• province: Haryana<br/>• plant_types: coal<br/>• currency: INR<br/>• financial_year: 04-03]
    end
    
    subgraph "🔧 TECHNICAL DATA LAYER"
        F1[🧠 Cache Analysis<br/>PlantDetailsExtractor]
        F2[💾 Cache Hits<br/>• name: ✅ (from org)<br/>• plant_type: ✅ (derived)]
        F3[❌ Missing Fields<br/>• lat, long<br/>• plant_address<br/>• units_id<br/>• grid_connectivity<br/>• ppa_details]
    end
    
    subgraph "🎯 RAG TARGETED SEARCH LAYER"
        G1[🔍 Latitude Search<br/>Query: 'latitude coordinate GPS']
        G2[🔍 Longitude Search<br/>Query: 'longitude coordinate GPS']
        G3[🔍 Address Search<br/>Query: 'district city location']
        G4[🔍 Grid Search<br/>Query: 'grid substation transmission']
        G5[🔍 PPA Search<br/>Query: 'power purchase agreement']
        G6[🔍 Units Search<br/>Query: 'generation units number']
    end
    
    subgraph "📄 TARGETED CONTENT LAYER"
        H1[📊 GPS Coordinates<br/>Pattern: ([0-9]+\.[0-9]+)<br/>Found: 28.607111, 76.656914]
        H2[📍 Location Data<br/>Text: 'Jharli village<br/>Jhajjar district, Haryana']
        H3[🔌 Grid Technical Docs<br/>PDF Processing<br/>Substation data extracted]
        H4[📄 PPA Regulatory Docs<br/>CERC Orders<br/>Contract structure found]
        H5[🔢 Unit Specifications<br/>Technical Reports<br/>Units: [1, 2]]
    end
    
    subgraph "🧠 RAG PROCESSING LAYER"
        I1[🎯 Pattern Matching<br/>Regex + LLM Validation]
        I2[📝 Text Extraction<br/>NLP + Context Analysis]
        I3[📊 JSON Parsing<br/>Pydantic Model Validation]
        I4[🔢 Number Extraction<br/>Integer List Processing]
    end
    
    subgraph "💾 OUTPUT GENERATION LAYER"
        J1[📊 Organizational JSON<br/>8/9 fields (89%)<br/>jhajjar_org_openai_rag_*.json]
        J2[🔧 Plant Technical JSON<br/>9/9 fields (100%)<br/>jhajjar_plant_openai_rag_*.json]
        J3[📈 Extraction Metadata<br/>Performance metrics<br/>jhajjar_extraction_info_*.json]
    end
    
    subgraph "📊 PERFORMANCE METRICS"
        K1[⏱️ Total Time: 405.9s<br/>🔍 Search: 4.5s<br/>📄 Scraping: Variable<br/>🧠 LLM: ~60s<br/>🎯 RAG: ~340s]
        K2[📈 Success Rates<br/>💾 Cache: 2/8 fields (25%)<br/>🎯 RAG: 6/6 fields (100%)<br/>📊 Overall: 17/18 fields (94%)]
    end
    
    %% Data Flow Connections
    A --> B1 & B2 & B3 & B4 & B5
    B1 & B2 & B3 & B4 & B5 --> C1
    C1 --> C2 --> C3
    C3 --> D1 --> D2 --> D3
    D3 --> E1 --> E2 --> E3
    E3 --> F1 --> F2 & F3
    F3 --> G1 & G2 & G3 & G4 & G5 & G6
    G1 --> H1
    G2 --> H1
    G3 --> H2
    G4 --> H3
    G5 --> H4
    G6 --> H5
    H1 --> I1
    H2 --> I2
    H3 --> I3
    H4 --> I3
    H5 --> I4
    I1 & I2 & I3 & I4 --> J1 & J2 & J3
    J1 & J2 & J3 --> K1 & K2
    
    %% Styling
    classDef input fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
    classDef init fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef search fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef content fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef llm fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef tech fill:#e0f2f1,stroke:#00796b,stroke-width:2px
    classDef rag fill:#fff8e1,stroke:#fbc02d,stroke-width:2px
    classDef output fill:#f1f8e9,stroke:#689f38,stroke-width:2px
    classDef metrics fill:#fafafa,stroke:#424242,stroke-width:2px
    
    class A input
    class B1,B2,B3,B4,B5 init
    class C1,C2,C3 search
    class D1,D2,D3 content
    class E1,E2,E3 llm
    class F1,F2,F3 tech
    class G1,G2,G3,G4,G5,G6,H1,H2,H3,H4,H5,I1,I2,I3,I4 rag
    class J1,J2,J3 output
    class K1,K2 metrics
```

## 🔧 Component Interaction Diagram

```mermaid
sequenceDiagram
    participant User as 👤 User
    participant Pipeline as 🚀 OpenAI RAG Pipeline
    participant SERP as 🔍 SERP API
    participant Scraper as 🌐 ScraperAPI
    participant OpenAI as 🧠 OpenAI GPT-4o-mini
    participant RAG as 🎯 RAG Engine
    participant Output as 💾 File System
    
    User->>Pipeline: run_openai_rag_pipeline.py
    Pipeline->>Pipeline: Initialize components
    
    Note over Pipeline: Phase 1: Initial Search & Scraping
    Pipeline->>SERP: Search "Jhajjar Power Plant"
    SERP-->>Pipeline: Top 5 URLs
    
    loop For each URL (5 total)
        Pipeline->>Scraper: async scrape_url(url)
        Scraper-->>Pipeline: Content (516-7880 chars)
    end
    
    Note over Pipeline: Phase 2: Organizational Extraction
    Pipeline->>OpenAI: Extract organization_name
    OpenAI-->>Pipeline: "CLP India Private Limited" (0.90 confidence)
    Pipeline->>OpenAI: Extract country_name
    OpenAI-->>Pipeline: "India" (0.90 confidence)
    Pipeline->>OpenAI: Extract province
    OpenAI-->>Pipeline: "Haryana" (0.90 confidence)
    Pipeline->>OpenAI: Extract plant_types
    OpenAI-->>Pipeline: ["coal"] (0.80 confidence)
    
    Note over Pipeline: Phase 3: Technical Data Analysis
    Pipeline->>Pipeline: Analyze cached content
    Pipeline->>Pipeline: Identify missing fields: lat, long, address, units, grid, ppa
    
    Note over Pipeline: Phase 4: RAG Targeted Searches
    loop For each missing field (6 total)
        Pipeline->>RAG: Generate targeted query
        RAG->>SERP: Field-specific search
        SERP-->>RAG: Targeted URLs (3 per field)
        
        loop For each targeted URL
            RAG->>Scraper: async scrape_url(targeted_url)
            Scraper-->>RAG: Targeted content
        end
        
        RAG->>RAG: Pattern matching + extraction
        RAG-->>Pipeline: Extracted field value
    end
    
    Note over Pipeline: Phase 5: Output Generation
    Pipeline->>Output: Save organizational JSON
    Pipeline->>Output: Save plant technical JSON
    Pipeline->>Output: Save extraction metadata
    Pipeline-->>User: ✅ Pipeline Complete (405.9s)
```

## 🎯 RAG Extraction Process Detail

```mermaid
flowchart LR
    subgraph "🔍 RAG Search Process"
        A[Missing Field:<br/>lat] --> B[Generate Query:<br/>'Jhajjar Power Plant<br/>latitude coordinate<br/>decimal degrees GPS']
        B --> C[SERP Search:<br/>3 targeted URLs]
        C --> D[Scrape Content:<br/>GPS coordinate data]
        D --> E[Pattern Matching:<br/>Regex: ([0-9]+\.[0-9]+)]
        E --> F[Validation:<br/>8 ≤ lat ≤ 37<br/>✅ 28.607111]
        F --> G[✅ Success:<br/>lat = "28.607111"]
    end
    
    subgraph "📊 Success Metrics"
        H[🎯 RAG Efficiency<br/>6/6 fields extracted<br/>100% success rate]
        I[⏱️ Time Distribution<br/>Search: 4.5s<br/>Initial Scraping: ~30s<br/>LLM Extraction: ~60s<br/>RAG Processing: ~340s]
        J[📈 Data Quality<br/>High precision coordinates<br/>Structured JSON output<br/>Validated field formats]
    end
    
    G --> H
    H --> I
    I --> J
```

## 🏗️ Technical Architecture Summary

### **🔧 Core Components**
- **OpenAI GPT-4o-mini**: Primary LLM for extraction and validation
- **SERP API**: Google search integration for URL discovery
- **ScraperAPI**: Async web scraping with retry mechanisms
- **RAG Engine**: Targeted search and pattern-based extraction
- **Pydantic Models**: Data validation and JSON structure

### **📊 Data Processing Pipeline**
1. **Broad Search** → Initial content gathering (5 sources)
2. **LLM Extraction** → Organizational data (8/9 fields)
3. **Cache Analysis** → Identify missing technical fields
4. **RAG Searches** → Targeted field-specific extraction (6 fields)
5. **Pattern Matching** → Regex + LLM hybrid validation
6. **JSON Output** → Structured data with metadata

### **⚡ Performance Characteristics**
- **Total Processing Time**: 6.8 minutes
- **Success Rate**: 94% overall (17/18 fields)
- **Cache Efficiency**: 25% fields from initial scraping
- **RAG Precision**: 100% success on targeted searches
- **Output Quality**: Validated JSON with confidence scores
