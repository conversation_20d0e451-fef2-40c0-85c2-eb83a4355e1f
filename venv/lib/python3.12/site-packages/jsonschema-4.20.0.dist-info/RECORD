../../../bin/jsonschema,sha256=fonBrBmYT2mxouFTHVwOjF-9runSK4dCBLMj0AzWvVk,255
jsonschema-4.20.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
jsonschema-4.20.0.dist-info/METADATA,sha256=wDoqdIUwZ8n2NOlLv7m1iLMKkxMTAup3BbgAF2ujLBM,8092
jsonschema-4.20.0.dist-info/RECORD,,
jsonschema-4.20.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
jsonschema-4.20.0.dist-info/WHEEL,sha256=9QBuHhg6FNW7lppboF2vKVbCGTVzsFykgRQjjlajrhA,87
jsonschema-4.20.0.dist-info/entry_points.txt,sha256=vO7rX4Fs_xIVJy2pnAtKgTSxfpnozAVQ0DjCmpMxnWE,51
jsonschema-4.20.0.dist-info/licenses/COPYING,sha256=T5KgFaE8TRoEC-8BiqE0MLTxvHO0Gxa7hGw0Z2bedDk,1057
jsonschema/__init__.py,sha256=LkPwscySlJ9lTOp7ZB1M7jQ8mbG7-bYG41iBwbZ-o9s,3941
jsonschema/__main__.py,sha256=iLsZf2upUB3ilBKTlMnyK-HHt2Cnnfkwwxi_c6gLvSA,115
jsonschema/__pycache__/__init__.cpython-312.pyc,,
jsonschema/__pycache__/__main__.cpython-312.pyc,,
jsonschema/__pycache__/_format.cpython-312.pyc,,
jsonschema/__pycache__/_keywords.cpython-312.pyc,,
jsonschema/__pycache__/_legacy_keywords.cpython-312.pyc,,
jsonschema/__pycache__/_types.cpython-312.pyc,,
jsonschema/__pycache__/_typing.cpython-312.pyc,,
jsonschema/__pycache__/_utils.cpython-312.pyc,,
jsonschema/__pycache__/cli.cpython-312.pyc,,
jsonschema/__pycache__/exceptions.cpython-312.pyc,,
jsonschema/__pycache__/protocols.cpython-312.pyc,,
jsonschema/__pycache__/validators.cpython-312.pyc,,
jsonschema/_format.py,sha256=OTCvvvKKmk7SGUa8N31CVpiMxF7cZ9l1WO2trR4kjB8,14773
jsonschema/_keywords.py,sha256=XH_6ed-ElmQ0kL3CbPHSAMIdJroikdhDXFsln5BaYdA,14592
jsonschema/_legacy_keywords.py,sha256=4zI2iaIzQBdeHT1MbiNt77HLEvjNW3Vz_rB1jPIL-jI,15288
jsonschema/_types.py,sha256=cQYlNKvI2UkxaoHfOVrQJ_JgsWxU0PYJ5E19XlnQ-J4,5352
jsonschema/_typing.py,sha256=NZhPhkBOn9INYZk8G69rDeuRamztgXCMLh10z9cfT6g,610
jsonschema/_utils.py,sha256=_NX2kZkpV_I_uwFfUggZEQUErEqY-f_it8vJSkXKaG4,10667
jsonschema/benchmarks/__init__.py,sha256=A0sQrxDBVHSyQ-8ru3L11hMXf3q9gVuB9x_YgHb4R9M,70
jsonschema/benchmarks/__pycache__/__init__.cpython-312.pyc,,
jsonschema/benchmarks/__pycache__/issue232.cpython-312.pyc,,
jsonschema/benchmarks/__pycache__/json_schema_test_suite.cpython-312.pyc,,
jsonschema/benchmarks/__pycache__/nested_schemas.cpython-312.pyc,,
jsonschema/benchmarks/__pycache__/subcomponents.cpython-312.pyc,,
jsonschema/benchmarks/__pycache__/unused_registry.cpython-312.pyc,,
jsonschema/benchmarks/__pycache__/validator_creation.cpython-312.pyc,,
jsonschema/benchmarks/issue232.py,sha256=3LLYLIlBGQnVuyyo2iAv-xky5P6PRFHANx4-zIIQOoE,521
jsonschema/benchmarks/issue232/issue.json,sha256=eaPOZjMRu5u8RpKrsA9uk7ucPZS5tkKG4D_hkOTQ3Hk,117105
jsonschema/benchmarks/json_schema_test_suite.py,sha256=PvfabpUYcF4_7csYDTcTauED8rnFEGYbdY5RqTXD08s,320
jsonschema/benchmarks/nested_schemas.py,sha256=JyK95iIABBmku7AIHpRhr9XKYAxZ1-g1Qh84oRT8FPU,1891
jsonschema/benchmarks/subcomponents.py,sha256=mfWy04Ibrn2Yeu5RlwoWa1eGfOU1L2UOjusRXgRnCeM,1112
jsonschema/benchmarks/unused_registry.py,sha256=Rf1H2Mw79YzDfkjnI-6-FFrXpR-N9NMel5qvht6Vbf4,939
jsonschema/benchmarks/validator_creation.py,sha256=UkUQlLAnussnr_KdCIdad6xx2pXxQLmYtsXoiirKeWQ,285
jsonschema/cli.py,sha256=Xj1RxubUSWti9epUFUostExxuOOOiZ3bMYzHLnV2pN0,8431
jsonschema/exceptions.py,sha256=qDpHDnBWNKrGCOsRA-4kFBlhac7igY2nS5FfN-CpgQk,13928
jsonschema/protocols.py,sha256=6OQ_ME0-rB0jfCkDpbKqqb4gFM0G0NUw6S38hszNBI0,7160
jsonschema/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
jsonschema/tests/__pycache__/__init__.cpython-312.pyc,,
jsonschema/tests/__pycache__/_suite.cpython-312.pyc,,
jsonschema/tests/__pycache__/fuzz_validate.cpython-312.pyc,,
jsonschema/tests/__pycache__/test_cli.cpython-312.pyc,,
jsonschema/tests/__pycache__/test_deprecations.cpython-312.pyc,,
jsonschema/tests/__pycache__/test_exceptions.cpython-312.pyc,,
jsonschema/tests/__pycache__/test_format.cpython-312.pyc,,
jsonschema/tests/__pycache__/test_jsonschema_test_suite.cpython-312.pyc,,
jsonschema/tests/__pycache__/test_types.cpython-312.pyc,,
jsonschema/tests/__pycache__/test_utils.cpython-312.pyc,,
jsonschema/tests/__pycache__/test_validators.cpython-312.pyc,,
jsonschema/tests/_suite.py,sha256=KMPsJ5Rvo3QuZL5emoIBYMatwIJsOxYCKwxb0jU3lZg,8062
jsonschema/tests/fuzz_validate.py,sha256=fUA7yTJIihaCwJplkUehZeyB84HcXEcqtY5oPJXIO7I,1114
jsonschema/tests/test_cli.py,sha256=4yK21qvPgsopcoW4DEmbze3Tc5h-gJKQJKGeAKDf9pA,28571
jsonschema/tests/test_deprecations.py,sha256=eLIBCeAmSrGzN81LiVFuLOf_8CThcwiQ7XvaazPis74,15712
jsonschema/tests/test_exceptions.py,sha256=8F39-3_jHgg4OvL7ynHMVNZpaOiWoF79mPM4LltnYxA,20122
jsonschema/tests/test_format.py,sha256=eVm5SMaWF2lOPO28bPAwNvkiQvHCQKy-MnuAgEchfEc,3188
jsonschema/tests/test_jsonschema_test_suite.py,sha256=xhqkEpVPUVM4TK_eZ1o4QojpkFFlAZi6_0GdMEXpvcQ,8084
jsonschema/tests/test_types.py,sha256=cF51KTDmdsx06MrIc4fXKt0X9fIsVgw5uhT8CamVa8U,6977
jsonschema/tests/test_utils.py,sha256=lJRVYyQeZQTUCTU_M3BhlkxPMgjsc8KQCd7U_Qkook8,3749
jsonschema/tests/test_validators.py,sha256=h1S9CJtbukON_SZO5hwgS9y-Bob2Lrk7RxpQC-Ku7Kk,86856
jsonschema/validators.py,sha256=LV66AJIO--llPArzpj_MODJNpdcxep1q4zzxj4QIx6U,46303
