"""
Smart unified pipeline with intelligent caching for both organizational and plant technical details.
"""
import asyncio
import logging
import json
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime

from src.models import OrganizationalDetails, PlantDetails, ScrapedContent
from src.config import config
from src.serp_client import SerpAPIClient, PowerPlantSearchOrchestrator
from src.scraper_client import ScraperAPIClient, ContentScrapeOrchestrator
from src.groq_client import GroqExtractionClient
from src.enhanced_extractor import AdaptiveExtractor
from src.plant_details_extractor import PlantDetailsExtractor
from src.field_analyzer import PlantFieldAnalyzer

logger = logging.getLogger(__name__)


class SmartUnifiedPowerPlantPipeline:
    """Smart unified pipeline with intelligent caching and selective searching."""

    def __init__(self):
        self.serp_api_key = config.pipeline.serp_api_key
        self.scraper_api_key = config.pipeline.scraper_api_key
        self.groq_api_key = config.pipeline.groq_api_key

        # Validate API keys
        if not self.scraper_api_key:
            raise ValueError("SCRAPER_API_KEY is required")
        if not self.groq_api_key:
            raise ValueError("GROQ_API_KEY is required")

        self.field_analyzer = PlantFieldAnalyzer()
        logger.info(f"Smart unified pipeline initialized with intelligent caching")

    async def extract_complete_plant_data_smart(
        self,
        plant_name: str,
        extract_organizational: bool = True,
        extract_technical: bool = True,
        use_cache_optimization: bool = True
    ) -> Tuple[Optional[OrganizationalDetails], Optional[PlantDetails], Dict]:
        """
        Extract both organizational and technical details with smart caching.

        Args:
            plant_name: Name of the power plant
            extract_organizational: Whether to extract organizational details
            extract_technical: Whether to extract technical details
            use_cache_optimization: Whether to use cache optimization for plant details

        Returns:
            Tuple of (OrganizationalDetails, PlantDetails, cache_info)
        """
        logger.info(f"Starting smart unified extraction for: {plant_name}")

        cache_info = {
            "organizational_search_time": 0,
            "plant_search_time": 0,
            "cache_hit_fields": [],
            "additional_searches": [],
            "total_api_calls": 0,
            "extraction_strategy": "smart_unified"
        }

        org_details = None
        plant_details = None

        try:
            # Phase 1: Always start with organizational search (provides base context)
            if extract_organizational:
                start_time = datetime.now()
                org_details, org_scraped_contents = await self._organizational_phase_with_cache(plant_name)
                cache_info["organizational_search_time"] = (datetime.now() - start_time).total_seconds()
            else:
                # If not extracting org details, still do a minimal search for context
                org_scraped_contents = await self._minimal_context_search(plant_name)
                cache_info["organizational_search_time"] = 0

            # Phase 2: Smart plant details extraction
            if extract_technical:
                start_time = datetime.now()

                if use_cache_optimization and org_scraped_contents:
                    # Use smart cache-optimized extraction
                    plant_details, plant_cache_info = await self._smart_plant_extraction_phase(
                        org_scraped_contents, plant_name, org_details
                    )
                    cache_info.update(plant_cache_info)
                else:
                    # Fallback to full search
                    plant_details = await self._full_plant_extraction_phase(plant_name)

                cache_info["plant_search_time"] = (datetime.now() - start_time).total_seconds()

            logger.info(f"Smart unified extraction completed for: {plant_name}")
            logger.info(f"Cache efficiency: {len(cache_info.get('cache_hit_fields', []))} fields from cache, "
                       f"{len(cache_info.get('additional_searches', []))} additional searches")

        except Exception as e:
            logger.error(f"Smart unified pipeline failed for {plant_name}: {e}")
            raise

        return org_details, plant_details, cache_info

    async def _organizational_phase_with_cache(
        self,
        plant_name: str
    ) -> Tuple[OrganizationalDetails, List[ScrapedContent]]:
        """Extract organizational details and return both results and scraped content for caching."""

        logger.info("Phase 1: Organizational extraction with cache preparation")

        # Search for organizational information
        async with SerpAPIClient(self.serp_api_key) as serp_client:
            search_orchestrator = PowerPlantSearchOrchestrator(serp_client)
            search_results = await search_orchestrator.comprehensive_search(plant_name)

        # Scrape content
        async with ScraperAPIClient(self.scraper_api_key) as scraper_client:
            scrape_orchestrator = ContentScrapeOrchestrator(scraper_client)
            scraped_contents = await scrape_orchestrator.scrape_search_results(
                search_results,
                max_pages_per_category=config.pipeline.max_scrape_pages
            )

        # Extract organizational details
        adaptive_extractor = AdaptiveExtractor(self.groq_api_key)
        org_details = await adaptive_extractor.extract_adaptively(scraped_contents, plant_name)

        logger.info(f"Organizational phase completed. Cached {len(scraped_contents)} content pieces for reuse")

        return org_details, scraped_contents

    async def _minimal_context_search(self, plant_name: str) -> List[ScrapedContent]:
        """Perform minimal search for context when not extracting organizational details."""

        logger.info("Performing minimal context search for plant details extraction")

        # Just do basic discovery search for context
        async with SerpAPIClient(self.serp_api_key) as serp_client:
            search_orchestrator = PowerPlantSearchOrchestrator(serp_client)

            # Basic search only
            basic_queries = [
                f"{plant_name} power plant",
                f"{plant_name} power station"
            ]

            search_results = {"basic_discovery": []}
            for query in basic_queries:
                try:
                    results = await serp_client.search(query, num_results=5)
                    search_results["basic_discovery"].extend(results)
                    await asyncio.sleep(1)
                except Exception as e:
                    logger.error(f"Minimal search failed for query '{query}': {e}")

        # Scrape minimal content
        async with ScraperAPIClient(self.scraper_api_key) as scraper_client:
            scrape_orchestrator = ContentScrapeOrchestrator(scraper_client)
            scraped_contents = await scrape_orchestrator.scrape_search_results(
                search_results,
                max_pages_per_category=3  # Reduced for minimal search
            )

        return scraped_contents

    async def _smart_plant_extraction_phase(
        self,
        cached_scraped_contents: List[ScrapedContent],
        plant_name: str,
        org_details: Optional[OrganizationalDetails]
    ) -> Tuple[PlantDetails, Dict]:
        """Smart plant extraction using cached content and selective additional searches."""

        logger.info("Phase 2: Smart plant extraction with cache optimization")

        # Initialize plant extractor
        groq_client = GroqExtractionClient(self.groq_api_key)
        plant_extractor = PlantDetailsExtractor(groq_client)

        # Step 1: Try extraction from cached content
        cache_result = await plant_extractor.extract_with_cache_optimization(
            cached_scraped_contents, plant_name, org_details
        )

        extracted_data = cache_result["extracted_data"]
        missing_fields = cache_result["missing_fields"]

        cache_info = {
            "cache_hit_fields": [k for k, v in extracted_data.items() if v not in [None, '', []]],
            "additional_searches": [],
            "total_api_calls": 0
        }

        # Step 2: Perform selective searches for missing critical fields
        if missing_fields:
            logger.info(f"Performing selective searches for {len(missing_fields)} missing fields: {missing_fields}")

            additional_data = await self._selective_field_search(plant_name, missing_fields)

            # Merge additional data
            for field, value in additional_data.items():
                if value and value not in [None, '', []]:
                    extracted_data[field] = value
                    cache_info["cache_hit_fields"].append(field)

            cache_info["additional_searches"] = list(set(self._get_search_categories_for_fields(missing_fields)))
            cache_info["total_api_calls"] = len(cache_info["additional_searches"]) * 2  # Estimate

        # Step 3: Create PlantDetails object
        try:
            # Process complex fields
            extracted_data = plant_extractor._process_complex_fields(extracted_data)
            plant_details = PlantDetails(**extracted_data)

            logger.info(f"Smart plant extraction completed. "
                       f"Cache hits: {len(cache_info['cache_hit_fields'])}, "
                       f"Additional searches: {len(cache_info['additional_searches'])}")

        except Exception as e:
            logger.error(f"Failed to create PlantDetails object: {e}")
            plant_details = PlantDetails()

        return plant_details, cache_info

    async def _selective_field_search(self, plant_name: str, missing_fields: List[str]) -> Dict[str, Any]:
        """Perform selective searches for specific missing fields."""

        # Determine which search categories are needed
        search_categories = self._get_search_categories_for_fields(missing_fields)

        if not search_categories:
            return {}

        logger.info(f"Performing selective search in {len(search_categories)} categories for missing fields")

        # Perform targeted searches
        async with SerpAPIClient(self.serp_api_key) as serp_client:
            search_orchestrator = PowerPlantSearchOrchestrator(serp_client)

            targeted_results = {}
            for category in search_categories:
                try:
                    if category == "grid_connectivity":
                        results = await self._search_grid_connectivity(search_orchestrator, plant_name)
                    elif category == "plant_coordinates":
                        results = await self._search_coordinates(search_orchestrator, plant_name)
                    elif category == "ppa_contracts":
                        results = await self._search_ppa_contracts(search_orchestrator, plant_name)
                    elif category == "plant_units":
                        results = await self._search_plant_units(search_orchestrator, plant_name)
                    else:
                        results = []

                    if results:
                        targeted_results[category] = results

                except Exception as e:
                    logger.error(f"Selective search failed for category {category}: {e}")

        # Scrape targeted results
        if targeted_results:
            async with ScraperAPIClient(self.scraper_api_key) as scraper_client:
                scrape_orchestrator = ContentScrapeOrchestrator(scraper_client)
                targeted_content = await scrape_orchestrator.scrape_search_results(
                    targeted_results,
                    max_pages_per_category=2  # Limited scraping for efficiency
                )

            # Extract from targeted content
            if targeted_content:
                groq_client = GroqExtractionClient(self.groq_api_key)
                plant_extractor = PlantDetailsExtractor(groq_client)

                additional_extraction = await plant_extractor.extract_all_plant_details(
                    targeted_content, plant_name
                )

                # Filter to only return the missing fields we were looking for
                filtered_results = {}
                for field in missing_fields:
                    if field in additional_extraction and additional_extraction[field] not in [None, '', []]:
                        filtered_results[field] = additional_extraction[field]

                return filtered_results

        return {}

    def _get_search_categories_for_fields(self, fields: List[str]) -> List[str]:
        """Map missing fields to search categories."""

        field_to_category = {
            "lat": "plant_coordinates",
            "long": "plant_coordinates",
            "plant_address": "plant_coordinates",
            "grid_connectivity_maps": "grid_connectivity",
            "ppa_details": "ppa_contracts",
            "units_id": "plant_units",
            "plant_type": "technical_details"
        }

        categories = []
        for field in fields:
            category = field_to_category.get(field)
            if category and category not in categories:
                categories.append(category)

        return categories

    async def _search_grid_connectivity(self, orchestrator, plant_name: str):
        """Targeted search for grid connectivity information."""
        queries = [f"{plant_name} substation connection", f"{plant_name} grid interconnection"]
        results = []
        for query in queries:
            try:
                search_results = await orchestrator.serp_client.search(query, num_results=3)
                results.extend(search_results)
                await asyncio.sleep(1)
            except Exception as e:
                logger.error(f"Grid connectivity search failed: {e}")
        return results

    async def _search_coordinates(self, orchestrator, plant_name: str):
        """Targeted search for coordinates information."""
        queries = [f"{plant_name} coordinates GPS", f"{plant_name} latitude longitude"]
        results = []
        for query in queries:
            try:
                search_results = await orchestrator.serp_client.search(query, num_results=3)
                results.extend(search_results)
                await asyncio.sleep(1)
            except Exception as e:
                logger.error(f"Coordinates search failed: {e}")
        return results

    async def _search_ppa_contracts(self, orchestrator, plant_name: str):
        """Targeted search for PPA contract information."""
        queries = [f"{plant_name} power purchase agreement", f"{plant_name} PPA contract"]
        results = []
        for query in queries:
            try:
                search_results = await orchestrator.serp_client.search(query, num_results=3)
                results.extend(search_results)
                await asyncio.sleep(1)
            except Exception as e:
                logger.error(f"PPA search failed: {e}")
        return results

    async def _search_plant_units(self, orchestrator, plant_name: str):
        """Targeted search for plant units information."""
        queries = [f"{plant_name} units turbines", f"{plant_name} generation units"]
        results = []
        for query in queries:
            try:
                search_results = await orchestrator.serp_client.search(query, num_results=3)
                results.extend(search_results)
                await asyncio.sleep(1)
            except Exception as e:
                logger.error(f"Plant units search failed: {e}")
        return results

    async def _full_plant_extraction_phase(self, plant_name: str) -> PlantDetails:
        """Fallback to full plant extraction without cache optimization."""

        logger.info("Performing full plant extraction (no cache optimization)")

        # This is the original approach - full search and extraction
        async with SerpAPIClient(self.serp_api_key) as serp_client:
            search_orchestrator = PowerPlantSearchOrchestrator(serp_client)
            search_results = await search_orchestrator.comprehensive_plant_details_search(plant_name)

        async with ScraperAPIClient(self.scraper_api_key) as scraper_client:
            scrape_orchestrator = ContentScrapeOrchestrator(scraper_client)
            scraped_contents = await scrape_orchestrator.scrape_search_results(
                search_results,
                max_pages_per_category=config.pipeline.max_scrape_pages
            )

        groq_client = GroqExtractionClient(self.groq_api_key)
        plant_extractor = PlantDetailsExtractor(groq_client)
        extracted_data = await plant_extractor.extract_all_plant_details(scraped_contents, plant_name)

        return PlantDetails(**extracted_data)

    async def save_results(
        self,
        org_details: Optional[OrganizationalDetails] = None,
        plant_details: Optional[PlantDetails] = None,
        cache_info: Optional[Dict] = None,
        org_output_path: str = "org_details.json",
        plant_output_path: str = "plant_details_output.json",
        cache_output_path: str = "cache_analysis.json"
    ):
        """Save extraction results and cache analysis to JSON files."""
        try:
            # Save organizational details
            if org_details:
                org_dict = org_details.model_dump()
                with open(org_output_path, 'w', encoding='utf-8') as f:
                    json.dump(org_dict, f, indent=4, ensure_ascii=False)
                logger.info(f"Organizational results saved to {org_output_path}")

            # Save plant details
            if plant_details:
                plant_dict = plant_details.model_dump()
                with open(plant_output_path, 'w', encoding='utf-8') as f:
                    json.dump(plant_dict, f, indent=4, ensure_ascii=False)
                logger.info(f"Plant details results saved to {plant_output_path}")

            # Save cache analysis
            if cache_info:
                with open(cache_output_path, 'w', encoding='utf-8') as f:
                    json.dump(cache_info, f, indent=4, ensure_ascii=False)
                logger.info(f"Cache analysis saved to {cache_output_path}")

        except Exception as e:
            logger.error(f"Failed to save results: {e}")
            raise


async def main():
    """Main function for testing the smart unified pipeline."""
    import sys

    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # Get plant name from command line or use default
    if len(sys.argv) > 1:
        plant_name = " ".join(sys.argv[1:])
    else:
        plant_name = "Vogtle Nuclear Power Plant"

    try:
        # Initialize smart pipeline
        pipeline = SmartUnifiedPowerPlantPipeline()

        # Extract data with smart caching
        org_details, plant_details, cache_info = await pipeline.extract_complete_plant_data_smart(
            plant_name,
            extract_organizational=True,
            extract_technical=True,
            use_cache_optimization=True
        )

        # Save results
        await pipeline.save_results(org_details, plant_details, cache_info)

        # Print results
        print("\n" + "="*70)
        print("SMART UNIFIED EXTRACTION RESULTS")
        print("="*70)

        # Cache efficiency summary
        if cache_info:
            print(f"\n🧠 CACHE EFFICIENCY:")
            print(f"   💾 Fields from cache: {len(cache_info.get('cache_hit_fields', []))}")
            print(f"   🔍 Additional searches: {len(cache_info.get('additional_searches', []))}")
            print(f"   ⏱️  Total time: {cache_info.get('organizational_search_time', 0) + cache_info.get('plant_search_time', 0):.1f}s")

        if org_details:
            print("\n📊 ORGANIZATIONAL DETAILS:")
            print("-" * 40)
            org_data = org_details.model_dump()
            filled_fields = sum(1 for v in org_data.values() if v not in [None, "", []])
            print(f"Extracted: {filled_fields}/{len(org_data)} fields")
            print(json.dumps(org_data, indent=2))

        if plant_details:
            print("\n🔧 PLANT TECHNICAL DETAILS:")
            print("-" * 40)
            plant_data = plant_details.model_dump()
            filled_fields = sum(1 for v in plant_data.values() if v not in [None, "", []])
            print(f"Extracted: {filled_fields}/{len(plant_data)} fields")
            print(json.dumps(plant_data, indent=2))

    except Exception as e:
        logger.error(f"Smart pipeline execution failed: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
