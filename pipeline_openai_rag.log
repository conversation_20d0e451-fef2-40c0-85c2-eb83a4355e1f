2025-05-28 11:57:06,199 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 11:57:06,199 - src.enhanced_extractor - INFO - Enhanced data extractor initialized with OpenAI
2025-05-28 11:57:06,204 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 11:57:06,216 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 11:57:06,216 - src.plant_details_extractor - INFO - Plant details extractor initialized with OpenAI
2025-05-28 11:57:06,216 - __main__ - INFO - 🔍 Starting OpenAI RAG extraction for: Jhajjar Power Plant
2025-05-28 11:57:06,216 - __main__ - INFO - 📡 Step 1: Searching for top 5 links
2025-05-28 11:57:06,216 - __main__ - ERROR - Error in OpenAI RAG extraction: name 'SerpAP<PERSON><PERSON>' is not defined
Traceback (most recent call last):
  File "/Users/<USER>/Documents/clem_transition/run_openai_rag_pipeline.py", line 86, in extract_plant_data_with_rag
    async with SerpAPIClient(self.serp_api_key) as serp_client:
               ^^^^^^^^^^^^^
NameError: name 'SerpAPIClient' is not defined
2025-05-28 11:57:06,218 - src.simple_pipeline - INFO - 📈 Extraction info saved to jhajjar_extraction_info_openai_rag_20250528_115706.json
2025-05-28 11:57:47,160 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 11:57:47,161 - src.enhanced_extractor - INFO - Enhanced data extractor initialized with OpenAI
2025-05-28 11:57:47,167 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 11:57:47,178 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 11:57:47,178 - src.plant_details_extractor - INFO - Plant details extractor initialized with OpenAI
2025-05-28 11:57:47,178 - __main__ - INFO - 🔍 Starting OpenAI RAG extraction for: Jhajjar Power Plant
2025-05-28 11:57:47,178 - __main__ - INFO - 📡 Step 1: Searching for top 5 links
2025-05-28 11:58:02,080 - __main__ - INFO - 🌐 Step 2: Scraping 5 top results
2025-05-28 11:58:02,081 - __main__ - INFO - Scraping 1/5: https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html
2025-05-28 11:58:10,085 - __main__ - WARNING - Failed to scrape https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html: RetryError[<Future at 0x117ba8b00 state=finished raised RuntimeError>]
2025-05-28 11:58:10,087 - __main__ - INFO - Scraping 2/5: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 11:58:18,090 - __main__ - WARNING - Failed to scrape https://en.wikipedia.org/wiki/Jhajjar_Power_Station: RetryError[<Future at 0x117c636e0 state=finished raised RuntimeError>]
2025-05-28 11:58:18,091 - __main__ - INFO - Scraping 3/5: https://www.gem.wiki/Mahatma_Gandhi_power_station
2025-05-28 11:58:26,094 - __main__ - WARNING - Failed to scrape https://www.gem.wiki/Mahatma_Gandhi_power_station: RetryError[<Future at 0x117cfd100 state=finished raised RuntimeError>]
2025-05-28 11:58:26,095 - __main__ - INFO - Scraping 4/5: https://apraava.com/projects/jhajjar-power-plant
2025-05-28 11:58:34,098 - __main__ - WARNING - Failed to scrape https://apraava.com/projects/jhajjar-power-plant: RetryError[<Future at 0x117cfd3a0 state=finished raised RuntimeError>]
2025-05-28 11:58:34,099 - __main__ - INFO - Scraping 5/5: https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr
2025-05-28 11:58:42,102 - __main__ - WARNING - Failed to scrape https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr: RetryError[<Future at 0x117cfd4c0 state=finished raised RuntimeError>]
2025-05-28 11:58:42,103 - __main__ - INFO - ✅ Initial scraping completed: 0 pages scraped
2025-05-28 11:58:42,103 - __main__ - INFO - 📊 Step 3: Extracting organizational details from scraped content
2025-05-28 11:58:42,103 - __main__ - ERROR - Error in OpenAI RAG extraction: 'AdaptiveExtractor' object has no attribute 'extract_organizational_details'
Traceback (most recent call last):
  File "/Users/<USER>/Documents/clem_transition/run_openai_rag_pipeline.py", line 116, in extract_plant_data_with_rag
    org_details = await self.enhanced_extractor.extract_organizational_details(scraped_contents, plant_name)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AdaptiveExtractor' object has no attribute 'extract_organizational_details'
2025-05-28 11:58:42,109 - src.simple_pipeline - INFO - 📈 Extraction info saved to jhajjar_extraction_info_openai_rag_20250528_115842.json
2025-05-28 11:59:23,098 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 11:59:23,098 - src.enhanced_extractor - INFO - Enhanced data extractor initialized with OpenAI
2025-05-28 11:59:23,104 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 11:59:23,115 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 11:59:23,115 - src.plant_details_extractor - INFO - Plant details extractor initialized with OpenAI
2025-05-28 11:59:23,115 - __main__ - INFO - 🔍 Starting OpenAI RAG extraction for: Jhajjar Power Plant
2025-05-28 11:59:23,115 - __main__ - INFO - 📡 Step 1: Searching for top 5 links
2025-05-28 11:59:25,623 - __main__ - INFO - 🌐 Step 2: Scraping 5 top results
2025-05-28 11:59:25,623 - __main__ - INFO - Scraping 1/5: https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html
2025-05-28 11:59:33,626 - __main__ - WARNING - Failed to scrape https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html: RetryError[<Future at 0x11467fa70 state=finished raised RuntimeError>]
2025-05-28 11:59:33,628 - __main__ - INFO - Scraping 2/5: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 11:59:41,631 - __main__ - WARNING - Failed to scrape https://en.wikipedia.org/wiki/Jhajjar_Power_Station: RetryError[<Future at 0x114607ad0 state=finished raised RuntimeError>]
2025-05-28 11:59:41,632 - __main__ - INFO - Scraping 3/5: https://www.gem.wiki/Mahatma_Gandhi_power_station
2025-05-28 11:59:49,636 - __main__ - WARNING - Failed to scrape https://www.gem.wiki/Mahatma_Gandhi_power_station: RetryError[<Future at 0x1146a98b0 state=finished raised RuntimeError>]
2025-05-28 11:59:49,637 - __main__ - INFO - Scraping 4/5: https://apraava.com/projects/jhajjar-power-plant
2025-05-28 11:59:57,643 - __main__ - WARNING - Failed to scrape https://apraava.com/projects/jhajjar-power-plant: RetryError[<Future at 0x1146a99d0 state=finished raised RuntimeError>]
2025-05-28 11:59:57,646 - __main__ - INFO - Scraping 5/5: https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr
2025-05-28 12:00:05,649 - __main__ - WARNING - Failed to scrape https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr: RetryError[<Future at 0x1146a9b20 state=finished raised RuntimeError>]
2025-05-28 12:00:05,650 - __main__ - INFO - ✅ Initial scraping completed: 0 pages scraped
2025-05-28 12:00:05,650 - __main__ - INFO - 📊 Step 3: Extracting organizational details from scraped content
2025-05-28 12:00:05,650 - __main__ - WARNING - No scraped content available, creating minimal org details
2025-05-28 12:00:05,650 - __main__ - INFO - ✅ Organizational extraction completed
2025-05-28 12:00:05,651 - __main__ - INFO - 🔧 Step 4: Extracting plant details with OpenAI + RAG approach
2025-05-28 12:00:05,651 - __main__ - INFO - 🧠 Analyzing cached content for plant details extraction
2025-05-28 12:00:05,651 - __main__ - WARNING - No scraped content available, using RAG-only approach
2025-05-28 12:00:05,651 - __main__ - INFO - ✅ Field 'lat' extracted from cache
2025-05-28 12:00:05,651 - __main__ - INFO - ✅ Field 'long' extracted from cache
2025-05-28 12:00:05,651 - __main__ - INFO - ✅ Field 'plant_address' extracted from cache
2025-05-28 12:00:05,651 - __main__ - INFO - ✅ Field 'name' extracted from cache
2025-05-28 12:00:05,651 - __main__ - INFO - ✅ Field 'plant_type' extracted from cache
2025-05-28 12:00:05,651 - __main__ - INFO - ✅ Field 'units_id' extracted from cache
2025-05-28 12:00:05,651 - __main__ - INFO - 🎯 Starting RAG searches for 2 missing fields
2025-05-28 12:00:05,651 - __main__ - INFO - 🎯 Searching specifically for 'grid_connectivity_maps' data
2025-05-28 12:00:05,652 - __main__ - INFO - 🔍 RAG search query: 'Jhajjar Power Plant grid connection substation transmission line rated capacity classification voltage level'
2025-05-28 12:00:19,434 - __main__ - WARNING - Failed to scrape targeted URL https://herc.gov.in/writereaddata/pdf/stdtrn(3).pdf: RetryError[<Future at 0x1146a9b50 state=finished raised RuntimeError>]
2025-05-28 12:00:27,438 - __main__ - WARNING - Failed to scrape targeted URL http://www.gajendrahaldea.in/pdf/Jhajjar-Transmission-MSS.pdf: RetryError[<Future at 0x1146aa090 state=finished raised RuntimeError>]
2025-05-28 12:00:35,442 - __main__ - WARNING - Failed to scrape targeted URL https://www.scribd.com/document/337002790/Manual-of-Specs-Standards-for-Power-Transmission-Systems-india: RetryError[<Future at 0x1146aa1b0 state=finished raised RuntimeError>]
2025-05-28 12:00:35,444 - __main__ - WARNING - No content found for 'grid_connectivity_maps'
2025-05-28 12:00:35,444 - __main__ - INFO - 🎯 Searching specifically for 'ppa_details' data
2025-05-28 12:00:35,444 - __main__ - INFO - 🔍 RAG search query: 'Jhajjar Power Plant power purchase agreement PPA contract capacity commencement termination date entity procuring power'
2025-05-28 12:00:46,568 - __main__ - WARNING - Failed to scrape targeted URL https://cercind.gov.in/2023/orders/637-MP-2020.pdf: RetryError[<Future at 0x114607ef0 state=finished raised RuntimeError>]
2025-05-28 12:00:54,571 - __main__ - WARNING - Failed to scrape targeted URL https://www.eqmagpro.com/petition-of-the-power-purchase-agreement-ppa-for-approval-of-the-amendment-of-the-ppa-jhajjar-power-limited-jpl-eq-mag-pro/: RetryError[<Future at 0x1143d78c0 state=finished raised RuntimeError>]
2025-05-28 12:00:54,572 - __main__ - WARNING - No content found for 'ppa_details'
2025-05-28 12:00:54,572 - __main__ - INFO - ✅ PlantDetails object created successfully
2025-05-28 12:00:54,572 - __main__ - INFO - 🎉 OpenAI RAG extraction completed for: Jhajjar Power Plant
2025-05-28 12:00:54,573 - __main__ - INFO - ⏱️  Total time: 91.5s
2025-05-28 12:00:54,573 - __main__ - INFO - 💾 Cache efficiency: 6 fields from cache, 2 targeted searches
2025-05-28 12:00:54,574 - src.simple_pipeline - ERROR - Error saving results: 'dict' object has no attribute 'model_dump'
2025-05-28 12:04:13,628 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 12:04:13,628 - src.enhanced_extractor - INFO - Enhanced data extractor initialized with OpenAI
2025-05-28 12:04:13,634 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 12:04:13,645 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 12:04:13,645 - src.plant_details_extractor - INFO - Plant details extractor initialized with OpenAI
2025-05-28 12:04:13,646 - __main__ - INFO - 🔍 Starting OpenAI RAG extraction for: Jhajjar Power Plant
2025-05-28 12:04:13,646 - __main__ - INFO - 📡 Step 1: Searching for top 5 links
2025-05-28 12:04:43,665 - src.serp_client - ERROR - Unexpected error in SERP API search: 
2025-05-28 12:04:50,727 - __main__ - INFO - 🌐 Step 2: Scraping 5 top results
2025-05-28 12:04:50,727 - __main__ - INFO - Scraping 1/5: https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html
2025-05-28 12:04:58,731 - __main__ - WARNING - Failed to scrape https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html: RetryError[<Future at 0x10b51ade0 state=finished raised RuntimeError>]
2025-05-28 12:04:58,732 - __main__ - INFO - Scraping 2/5: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 12:05:06,736 - __main__ - WARNING - Failed to scrape https://en.wikipedia.org/wiki/Jhajjar_Power_Station: RetryError[<Future at 0x10b395970 state=finished raised RuntimeError>]
2025-05-28 12:05:06,738 - __main__ - INFO - Scraping 3/5: https://www.gem.wiki/Mahatma_Gandhi_power_station
2025-05-28 12:05:14,741 - __main__ - WARNING - Failed to scrape https://www.gem.wiki/Mahatma_Gandhi_power_station: RetryError[<Future at 0x10b7070b0 state=finished raised RuntimeError>]
2025-05-28 12:05:14,742 - __main__ - INFO - Scraping 4/5: https://apraava.com/projects/jhajjar-power-plant
2025-05-28 12:05:22,745 - __main__ - WARNING - Failed to scrape https://apraava.com/projects/jhajjar-power-plant: RetryError[<Future at 0x10b707290 state=finished raised RuntimeError>]
2025-05-28 12:05:22,747 - __main__ - INFO - Scraping 5/5: https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr
2025-05-28 12:05:30,750 - __main__ - WARNING - Failed to scrape https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr: RetryError[<Future at 0x10b7073b0 state=finished raised RuntimeError>]
2025-05-28 12:05:30,751 - __main__ - INFO - ✅ Initial scraping completed: 0 pages scraped
2025-05-28 12:05:30,751 - __main__ - INFO - 📊 Step 3: Extracting organizational details from scraped content
2025-05-28 12:05:30,751 - __main__ - WARNING - No scraped content available, creating minimal org details
2025-05-28 12:05:30,751 - __main__ - INFO - ✅ Organizational extraction completed
2025-05-28 12:05:30,751 - __main__ - INFO - 🔧 Step 4: Extracting plant details with OpenAI + RAG approach
2025-05-28 12:05:30,752 - __main__ - INFO - 🧠 Analyzing cached content for plant details extraction
2025-05-28 12:05:30,752 - __main__ - WARNING - No scraped content available, using RAG-only approach
2025-05-28 12:05:30,752 - __main__ - INFO - ✅ Field 'lat' extracted from cache
2025-05-28 12:05:30,752 - __main__ - INFO - ✅ Field 'long' extracted from cache
2025-05-28 12:05:30,752 - __main__ - INFO - ✅ Field 'plant_address' extracted from cache
2025-05-28 12:05:30,752 - __main__ - INFO - ✅ Field 'name' extracted from cache
2025-05-28 12:05:30,752 - __main__ - INFO - ✅ Field 'plant_type' extracted from cache
2025-05-28 12:05:30,752 - __main__ - INFO - ✅ Field 'units_id' extracted from cache
2025-05-28 12:05:30,753 - __main__ - INFO - 🎯 Starting RAG searches for 2 missing fields
2025-05-28 12:05:30,753 - __main__ - INFO - 🎯 Searching specifically for 'grid_connectivity_maps' data
2025-05-28 12:05:30,753 - __main__ - INFO - 🔍 RAG search query: 'Jhajjar Power Plant grid connection substation transmission line rated capacity classification voltage level'
2025-05-28 12:05:55,987 - __main__ - WARNING - Failed to scrape targeted URL https://herc.gov.in/writereaddata/pdf/stdtrn(3).pdf: RetryError[<Future at 0x10b706f30 state=finished raised RuntimeError>]
2025-05-28 12:13:15,373 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 12:13:15,373 - src.enhanced_extractor - INFO - Enhanced data extractor initialized with OpenAI
2025-05-28 12:13:15,379 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 12:13:15,390 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 12:13:15,390 - src.plant_details_extractor - INFO - Plant details extractor initialized with OpenAI
2025-05-28 12:13:15,390 - __main__ - INFO - 🔍 Starting OpenAI RAG extraction for: Jhajjar Power Plant
2025-05-28 12:13:15,390 - __main__ - INFO - 📡 Step 1: Searching for top 5 links
2025-05-28 12:13:20,863 - __main__ - INFO - 🌐 Step 2: Scraping 5 top results
2025-05-28 12:13:20,863 - __main__ - INFO - Scraping 1/5: https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html
2025-05-28 12:13:28,867 - __main__ - WARNING - Failed to scrape https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html: RetryError[<Future at 0x112c484d0 state=finished raised RuntimeError>]
2025-05-28 12:13:28,868 - __main__ - INFO - Scraping 2/5: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 12:13:36,871 - __main__ - WARNING - Failed to scrape https://en.wikipedia.org/wiki/Jhajjar_Power_Station: RetryError[<Future at 0x112c6a270 state=finished raised RuntimeError>]
2025-05-28 12:13:36,872 - __main__ - INFO - Scraping 3/5: https://www.gem.wiki/Mahatma_Gandhi_power_station
2025-05-28 12:13:44,875 - __main__ - WARNING - Failed to scrape https://www.gem.wiki/Mahatma_Gandhi_power_station: RetryError[<Future at 0x112c491c0 state=finished raised RuntimeError>]
2025-05-28 12:13:44,875 - __main__ - INFO - Scraping 4/5: https://apraava.com/projects/jhajjar-power-plant
2025-05-28 12:13:52,878 - __main__ - WARNING - Failed to scrape https://apraava.com/projects/jhajjar-power-plant: RetryError[<Future at 0x112e7a900 state=finished raised RuntimeError>]
2025-05-28 12:13:52,879 - __main__ - INFO - Scraping 5/5: https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr
2025-05-28 12:14:00,883 - __main__ - WARNING - Failed to scrape https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr: RetryError[<Future at 0x1138a6c90 state=finished raised RuntimeError>]
2025-05-28 12:14:00,884 - __main__ - INFO - ✅ Initial scraping completed: 0 pages scraped
2025-05-28 12:14:00,884 - __main__ - INFO - 📊 Step 3: Extracting organizational details from scraped content
2025-05-28 12:14:00,884 - __main__ - WARNING - No scraped content available, creating minimal org details
2025-05-28 12:14:00,884 - __main__ - INFO - ✅ Organizational extraction completed
2025-05-28 12:14:00,884 - __main__ - INFO - 🔧 Step 4: Extracting plant details with OpenAI + RAG approach
2025-05-28 12:14:00,885 - __main__ - INFO - 🧠 Analyzing cached content for plant details extraction
2025-05-28 12:14:00,885 - __main__ - WARNING - No scraped content available, using RAG-only approach
2025-05-28 12:14:00,885 - __main__ - INFO - ✅ Field 'lat' extracted from cache
2025-05-28 12:14:00,885 - __main__ - INFO - ✅ Field 'long' extracted from cache
2025-05-28 12:14:00,885 - __main__ - INFO - ✅ Field 'plant_address' extracted from cache
2025-05-28 12:14:00,885 - __main__ - INFO - ✅ Field 'name' extracted from cache
2025-05-28 12:14:00,885 - __main__ - INFO - ✅ Field 'plant_type' extracted from cache
2025-05-28 12:14:00,885 - __main__ - INFO - ✅ Field 'units_id' extracted from cache
2025-05-28 12:14:00,885 - __main__ - INFO - 🎯 Starting RAG searches for 2 missing fields
2025-05-28 12:14:00,885 - __main__ - INFO - 🎯 Searching specifically for 'grid_connectivity_maps' data
2025-05-28 12:14:00,885 - __main__ - INFO - 🔍 RAG search query: 'Jhajjar Power Plant grid connection substation transmission line rated capacity classification voltage level'
2025-05-28 12:14:10,893 - __main__ - WARNING - Failed to scrape targeted URL https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf: RetryError[<Future at 0x1138a7140 state=finished raised RuntimeError>]
2025-05-28 12:14:18,898 - __main__ - WARNING - Failed to scrape targeted URL https://herc.gov.in/WriteReadData/Orders/O20161006.pdf: RetryError[<Future at 0x1138a7680 state=finished raised RuntimeError>]
2025-05-28 12:14:26,903 - __main__ - WARNING - Failed to scrape targeted URL http://www.gajendrahaldea.in/pdf/Jhajjar-Transmission-MSS.pdf: RetryError[<Future at 0x1138a77a0 state=finished raised RuntimeError>]
2025-05-28 12:14:26,905 - __main__ - INFO - 🧠 Using knowledge-based fallback for 'grid_connectivity_maps'
2025-05-28 12:14:26,905 - __main__ - INFO - 💡 Used knowledge-based fallback for 'grid_connectivity_maps': [{'details': [{'substation_name': 'Jhajjar Substation', 'substation_type': '400 kV transmission substation', 'capacity': '1320 MW', 'latitude': '28.607111', 'longitude': '76.6565', 'projects': [{'distance': '0 km'}]}]}]
2025-05-28 12:14:26,905 - __main__ - INFO - 🎯 Searching specifically for 'ppa_details' data
2025-05-28 12:14:26,906 - __main__ - INFO - 🔍 RAG search query: 'Jhajjar Power Plant power purchase agreement PPA contract capacity commencement termination date entity procuring power'
2025-05-28 12:15:15,652 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 12:15:15,652 - src.enhanced_extractor - INFO - Enhanced data extractor initialized with OpenAI
2025-05-28 12:15:15,658 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 12:15:15,678 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 12:15:15,678 - src.plant_details_extractor - INFO - Plant details extractor initialized with OpenAI
2025-05-28 12:15:15,678 - __main__ - INFO - 🔍 Starting OpenAI RAG extraction for: Jhajjar Power Plant
2025-05-28 12:15:15,678 - __main__ - INFO - 📡 Step 1: Searching for top 5 links
2025-05-28 12:15:46,683 - src.serp_client - ERROR - Unexpected error in SERP API search: 
2025-05-28 12:16:21,679 - src.serp_client - ERROR - Unexpected error in SERP API search: 
2025-05-28 12:16:56,680 - src.serp_client - ERROR - Unexpected error in SERP API search: 
2025-05-28 12:16:56,682 - __main__ - ERROR - Error in OpenAI RAG extraction: RetryError[<Future at 0x10858c080 state=finished raised TimeoutError>]
Traceback (most recent call last):
  File "/Users/<USER>/miniconda3/lib/python3.12/site-packages/tenacity/_asyncio.py", line 50, in __call__
    result = await fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/clem_transition/src/serp_client.py", line 65, in search
    async with self.session.get(self.base_url, params=params) as response:
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/lib/python3.12/site-packages/aiohttp/client.py", line 1187, in __aenter__
    self._resp = await self._coro
                 ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/lib/python3.12/site-packages/aiohttp/client.py", line 601, in _request
    await resp.start(conn)
  File "/Users/<USER>/miniconda3/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 960, in start
    with self._timer:
         ^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/lib/python3.12/site-packages/aiohttp/helpers.py", line 735, in __exit__
    raise asyncio.TimeoutError from None
TimeoutError

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Documents/clem_transition/run_openai_rag_pipeline.py", line 88, in extract_plant_data_with_rag
    search_results = await serp_client.search(f"{plant_name} power plant", num_results=5)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/lib/python3.12/site-packages/tenacity/_asyncio.py", line 88, in async_wrapped
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/lib/python3.12/site-packages/tenacity/_asyncio.py", line 47, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/lib/python3.12/site-packages/tenacity/__init__.py", line 326, in iter
    raise retry_exc from fut.exception()
tenacity.RetryError: RetryError[<Future at 0x10858c080 state=finished raised TimeoutError>]
2025-05-28 12:16:56,694 - src.simple_pipeline - INFO - 📈 Extraction info saved to jhajjar_extraction_info_openai_rag_20250528_121656.json
2025-05-28 12:23:36,422 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 12:23:36,422 - src.enhanced_extractor - INFO - Enhanced data extractor initialized with OpenAI
2025-05-28 12:23:36,430 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 12:23:36,442 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 12:23:36,442 - src.plant_details_extractor - INFO - Plant details extractor initialized with OpenAI
2025-05-28 12:23:36,442 - __main__ - INFO - 🔍 Starting OpenAI RAG extraction for: Jhajjar Power Plant
2025-05-28 12:23:36,442 - __main__ - INFO - 📡 Step 1: Searching for top 5 links
2025-05-28 12:23:40,903 - __main__ - INFO - 🌐 Step 2: Scraping 5 top results
2025-05-28 12:23:40,903 - __main__ - INFO - Scraping 1/5: https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html
2025-05-28 12:23:48,907 - __main__ - WARNING - Failed to scrape https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html: RetryError[<Future at 0x10b57f020 state=finished raised RuntimeError>]
2025-05-28 12:23:48,907 - __main__ - INFO - Scraping 2/5: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 12:26:27,007 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 12:26:27,008 - src.enhanced_extractor - INFO - Enhanced data extractor initialized with OpenAI
2025-05-28 12:26:27,013 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 12:26:27,025 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 12:26:27,025 - src.plant_details_extractor - INFO - Plant details extractor initialized with OpenAI
2025-05-28 12:26:27,025 - __main__ - INFO - 🔍 Starting OpenAI RAG extraction for: Jhajjar Power Plant
2025-05-28 12:26:27,025 - __main__ - INFO - 📡 Step 1: Searching for top 5 links
2025-05-28 12:26:38,184 - __main__ - INFO - 🌐 Step 2: Scraping 5 top results
2025-05-28 12:26:38,184 - __main__ - INFO - Scraping 1/5: https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html
2025-05-28 12:26:40,453 - __main__ - INFO - Successfully scraped https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html (516 chars)
2025-05-28 12:26:41,454 - __main__ - INFO - Scraping 2/5: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 12:26:43,286 - __main__ - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-28 12:26:44,288 - __main__ - INFO - Scraping 3/5: https://www.gem.wiki/Mahatma_Gandhi_power_station
2025-05-28 12:26:45,523 - __main__ - INFO - Successfully scraped https://www.gem.wiki/Mahatma_Gandhi_power_station (7880 chars)
2025-05-28 12:26:46,524 - __main__ - INFO - Scraping 4/5: https://apraava.com/projects/jhajjar-power-plant
2025-05-28 12:26:48,450 - __main__ - INFO - Successfully scraped https://apraava.com/projects/jhajjar-power-plant (791 chars)
2025-05-28 12:26:49,451 - __main__ - INFO - Scraping 5/5: https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr
2025-05-28 12:26:52,241 - __main__ - INFO - Successfully scraped https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr (3864 chars)
2025-05-28 12:26:53,243 - __main__ - INFO - ✅ Initial scraping completed: 5 pages scraped
2025-05-28 12:26:53,244 - __main__ - INFO - 📊 Step 3: Extracting organizational details from scraped content
2025-05-28 12:26:53,244 - __main__ - ERROR - Error in OpenAI RAG extraction: 'AdaptiveExtractor' object has no attribute 'extract_org_details'
Traceback (most recent call last):
  File "/Users/<USER>/Documents/clem_transition/run_openai_rag_pipeline.py", line 118, in extract_plant_data_with_rag
    org_details = await self.enhanced_extractor.extract_org_details(scraped_contents, plant_name)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AdaptiveExtractor' object has no attribute 'extract_org_details'
2025-05-28 12:26:53,249 - src.simple_pipeline - INFO - 📈 Extraction info saved to jhajjar_extraction_info_openai_rag_20250528_122653.json
