2025-05-28 11:57:06,199 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 11:57:06,199 - src.enhanced_extractor - INFO - Enhanced data extractor initialized with OpenAI
2025-05-28 11:57:06,204 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 11:57:06,216 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 11:57:06,216 - src.plant_details_extractor - INFO - Plant details extractor initialized with OpenAI
2025-05-28 11:57:06,216 - __main__ - INFO - 🔍 Starting OpenAI RAG extraction for: Jhajjar Power Plant
2025-05-28 11:57:06,216 - __main__ - INFO - 📡 Step 1: Searching for top 5 links
2025-05-28 11:57:06,216 - __main__ - ERROR - Error in OpenAI RAG extraction: name 'SerpAP<PERSON><PERSON>' is not defined
Traceback (most recent call last):
  File "/Users/<USER>/Documents/clem_transition/run_openai_rag_pipeline.py", line 86, in extract_plant_data_with_rag
    async with SerpAPIClient(self.serp_api_key) as serp_client:
               ^^^^^^^^^^^^^
NameError: name 'SerpAPIClient' is not defined
2025-05-28 11:57:06,218 - src.simple_pipeline - INFO - 📈 Extraction info saved to jhajjar_extraction_info_openai_rag_20250528_115706.json
2025-05-28 11:57:47,160 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 11:57:47,161 - src.enhanced_extractor - INFO - Enhanced data extractor initialized with OpenAI
2025-05-28 11:57:47,167 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 11:57:47,178 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 11:57:47,178 - src.plant_details_extractor - INFO - Plant details extractor initialized with OpenAI
2025-05-28 11:57:47,178 - __main__ - INFO - 🔍 Starting OpenAI RAG extraction for: Jhajjar Power Plant
2025-05-28 11:57:47,178 - __main__ - INFO - 📡 Step 1: Searching for top 5 links
2025-05-28 11:58:02,080 - __main__ - INFO - 🌐 Step 2: Scraping 5 top results
2025-05-28 11:58:02,081 - __main__ - INFO - Scraping 1/5: https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html
2025-05-28 11:58:10,085 - __main__ - WARNING - Failed to scrape https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html: RetryError[<Future at 0x117ba8b00 state=finished raised RuntimeError>]
2025-05-28 11:58:10,087 - __main__ - INFO - Scraping 2/5: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 11:58:18,090 - __main__ - WARNING - Failed to scrape https://en.wikipedia.org/wiki/Jhajjar_Power_Station: RetryError[<Future at 0x117c636e0 state=finished raised RuntimeError>]
2025-05-28 11:58:18,091 - __main__ - INFO - Scraping 3/5: https://www.gem.wiki/Mahatma_Gandhi_power_station
2025-05-28 11:58:26,094 - __main__ - WARNING - Failed to scrape https://www.gem.wiki/Mahatma_Gandhi_power_station: RetryError[<Future at 0x117cfd100 state=finished raised RuntimeError>]
2025-05-28 11:58:26,095 - __main__ - INFO - Scraping 4/5: https://apraava.com/projects/jhajjar-power-plant
2025-05-28 11:58:34,098 - __main__ - WARNING - Failed to scrape https://apraava.com/projects/jhajjar-power-plant: RetryError[<Future at 0x117cfd3a0 state=finished raised RuntimeError>]
2025-05-28 11:58:34,099 - __main__ - INFO - Scraping 5/5: https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr
2025-05-28 11:58:42,102 - __main__ - WARNING - Failed to scrape https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr: RetryError[<Future at 0x117cfd4c0 state=finished raised RuntimeError>]
2025-05-28 11:58:42,103 - __main__ - INFO - ✅ Initial scraping completed: 0 pages scraped
2025-05-28 11:58:42,103 - __main__ - INFO - 📊 Step 3: Extracting organizational details from scraped content
2025-05-28 11:58:42,103 - __main__ - ERROR - Error in OpenAI RAG extraction: 'AdaptiveExtractor' object has no attribute 'extract_organizational_details'
Traceback (most recent call last):
  File "/Users/<USER>/Documents/clem_transition/run_openai_rag_pipeline.py", line 116, in extract_plant_data_with_rag
    org_details = await self.enhanced_extractor.extract_organizational_details(scraped_contents, plant_name)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AdaptiveExtractor' object has no attribute 'extract_organizational_details'
2025-05-28 11:58:42,109 - src.simple_pipeline - INFO - 📈 Extraction info saved to jhajjar_extraction_info_openai_rag_20250528_115842.json
2025-05-28 11:59:23,098 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 11:59:23,098 - src.enhanced_extractor - INFO - Enhanced data extractor initialized with OpenAI
2025-05-28 11:59:23,104 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 11:59:23,115 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 11:59:23,115 - src.plant_details_extractor - INFO - Plant details extractor initialized with OpenAI
2025-05-28 11:59:23,115 - __main__ - INFO - 🔍 Starting OpenAI RAG extraction for: Jhajjar Power Plant
2025-05-28 11:59:23,115 - __main__ - INFO - 📡 Step 1: Searching for top 5 links
2025-05-28 11:59:25,623 - __main__ - INFO - 🌐 Step 2: Scraping 5 top results
2025-05-28 11:59:25,623 - __main__ - INFO - Scraping 1/5: https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html
2025-05-28 11:59:33,626 - __main__ - WARNING - Failed to scrape https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html: RetryError[<Future at 0x11467fa70 state=finished raised RuntimeError>]
2025-05-28 11:59:33,628 - __main__ - INFO - Scraping 2/5: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 11:59:41,631 - __main__ - WARNING - Failed to scrape https://en.wikipedia.org/wiki/Jhajjar_Power_Station: RetryError[<Future at 0x114607ad0 state=finished raised RuntimeError>]
2025-05-28 11:59:41,632 - __main__ - INFO - Scraping 3/5: https://www.gem.wiki/Mahatma_Gandhi_power_station
2025-05-28 11:59:49,636 - __main__ - WARNING - Failed to scrape https://www.gem.wiki/Mahatma_Gandhi_power_station: RetryError[<Future at 0x1146a98b0 state=finished raised RuntimeError>]
2025-05-28 11:59:49,637 - __main__ - INFO - Scraping 4/5: https://apraava.com/projects/jhajjar-power-plant
2025-05-28 11:59:57,643 - __main__ - WARNING - Failed to scrape https://apraava.com/projects/jhajjar-power-plant: RetryError[<Future at 0x1146a99d0 state=finished raised RuntimeError>]
2025-05-28 11:59:57,646 - __main__ - INFO - Scraping 5/5: https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr
2025-05-28 12:00:05,649 - __main__ - WARNING - Failed to scrape https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr: RetryError[<Future at 0x1146a9b20 state=finished raised RuntimeError>]
2025-05-28 12:00:05,650 - __main__ - INFO - ✅ Initial scraping completed: 0 pages scraped
2025-05-28 12:00:05,650 - __main__ - INFO - 📊 Step 3: Extracting organizational details from scraped content
2025-05-28 12:00:05,650 - __main__ - WARNING - No scraped content available, creating minimal org details
2025-05-28 12:00:05,650 - __main__ - INFO - ✅ Organizational extraction completed
2025-05-28 12:00:05,651 - __main__ - INFO - 🔧 Step 4: Extracting plant details with OpenAI + RAG approach
2025-05-28 12:00:05,651 - __main__ - INFO - 🧠 Analyzing cached content for plant details extraction
2025-05-28 12:00:05,651 - __main__ - WARNING - No scraped content available, using RAG-only approach
2025-05-28 12:00:05,651 - __main__ - INFO - ✅ Field 'lat' extracted from cache
2025-05-28 12:00:05,651 - __main__ - INFO - ✅ Field 'long' extracted from cache
2025-05-28 12:00:05,651 - __main__ - INFO - ✅ Field 'plant_address' extracted from cache
2025-05-28 12:00:05,651 - __main__ - INFO - ✅ Field 'name' extracted from cache
2025-05-28 12:00:05,651 - __main__ - INFO - ✅ Field 'plant_type' extracted from cache
2025-05-28 12:00:05,651 - __main__ - INFO - ✅ Field 'units_id' extracted from cache
2025-05-28 12:00:05,651 - __main__ - INFO - 🎯 Starting RAG searches for 2 missing fields
2025-05-28 12:00:05,651 - __main__ - INFO - 🎯 Searching specifically for 'grid_connectivity_maps' data
2025-05-28 12:00:05,652 - __main__ - INFO - 🔍 RAG search query: 'Jhajjar Power Plant grid connection substation transmission line rated capacity classification voltage level'
2025-05-28 12:00:19,434 - __main__ - WARNING - Failed to scrape targeted URL https://herc.gov.in/writereaddata/pdf/stdtrn(3).pdf: RetryError[<Future at 0x1146a9b50 state=finished raised RuntimeError>]
2025-05-28 12:00:27,438 - __main__ - WARNING - Failed to scrape targeted URL http://www.gajendrahaldea.in/pdf/Jhajjar-Transmission-MSS.pdf: RetryError[<Future at 0x1146aa090 state=finished raised RuntimeError>]
2025-05-28 12:00:35,442 - __main__ - WARNING - Failed to scrape targeted URL https://www.scribd.com/document/337002790/Manual-of-Specs-Standards-for-Power-Transmission-Systems-india: RetryError[<Future at 0x1146aa1b0 state=finished raised RuntimeError>]
2025-05-28 12:00:35,444 - __main__ - WARNING - No content found for 'grid_connectivity_maps'
2025-05-28 12:00:35,444 - __main__ - INFO - 🎯 Searching specifically for 'ppa_details' data
2025-05-28 12:00:35,444 - __main__ - INFO - 🔍 RAG search query: 'Jhajjar Power Plant power purchase agreement PPA contract capacity commencement termination date entity procuring power'
2025-05-28 12:00:46,568 - __main__ - WARNING - Failed to scrape targeted URL https://cercind.gov.in/2023/orders/637-MP-2020.pdf: RetryError[<Future at 0x114607ef0 state=finished raised RuntimeError>]
2025-05-28 12:00:54,571 - __main__ - WARNING - Failed to scrape targeted URL https://www.eqmagpro.com/petition-of-the-power-purchase-agreement-ppa-for-approval-of-the-amendment-of-the-ppa-jhajjar-power-limited-jpl-eq-mag-pro/: RetryError[<Future at 0x1143d78c0 state=finished raised RuntimeError>]
2025-05-28 12:00:54,572 - __main__ - WARNING - No content found for 'ppa_details'
2025-05-28 12:00:54,572 - __main__ - INFO - ✅ PlantDetails object created successfully
2025-05-28 12:00:54,572 - __main__ - INFO - 🎉 OpenAI RAG extraction completed for: Jhajjar Power Plant
2025-05-28 12:00:54,573 - __main__ - INFO - ⏱️  Total time: 91.5s
2025-05-28 12:00:54,573 - __main__ - INFO - 💾 Cache efficiency: 6 fields from cache, 2 targeted searches
2025-05-28 12:00:54,574 - src.simple_pipeline - ERROR - Error saving results: 'dict' object has no attribute 'model_dump'
2025-05-28 12:04:13,628 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 12:04:13,628 - src.enhanced_extractor - INFO - Enhanced data extractor initialized with OpenAI
2025-05-28 12:04:13,634 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 12:04:13,645 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 12:04:13,645 - src.plant_details_extractor - INFO - Plant details extractor initialized with OpenAI
2025-05-28 12:04:13,646 - __main__ - INFO - 🔍 Starting OpenAI RAG extraction for: Jhajjar Power Plant
2025-05-28 12:04:13,646 - __main__ - INFO - 📡 Step 1: Searching for top 5 links
2025-05-28 12:04:43,665 - src.serp_client - ERROR - Unexpected error in SERP API search: 
2025-05-28 12:04:50,727 - __main__ - INFO - 🌐 Step 2: Scraping 5 top results
2025-05-28 12:04:50,727 - __main__ - INFO - Scraping 1/5: https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html
2025-05-28 12:04:58,731 - __main__ - WARNING - Failed to scrape https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html: RetryError[<Future at 0x10b51ade0 state=finished raised RuntimeError>]
2025-05-28 12:04:58,732 - __main__ - INFO - Scraping 2/5: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 12:05:06,736 - __main__ - WARNING - Failed to scrape https://en.wikipedia.org/wiki/Jhajjar_Power_Station: RetryError[<Future at 0x10b395970 state=finished raised RuntimeError>]
2025-05-28 12:05:06,738 - __main__ - INFO - Scraping 3/5: https://www.gem.wiki/Mahatma_Gandhi_power_station
2025-05-28 12:05:14,741 - __main__ - WARNING - Failed to scrape https://www.gem.wiki/Mahatma_Gandhi_power_station: RetryError[<Future at 0x10b7070b0 state=finished raised RuntimeError>]
2025-05-28 12:05:14,742 - __main__ - INFO - Scraping 4/5: https://apraava.com/projects/jhajjar-power-plant
2025-05-28 12:05:22,745 - __main__ - WARNING - Failed to scrape https://apraava.com/projects/jhajjar-power-plant: RetryError[<Future at 0x10b707290 state=finished raised RuntimeError>]
2025-05-28 12:05:22,747 - __main__ - INFO - Scraping 5/5: https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr
2025-05-28 12:05:30,750 - __main__ - WARNING - Failed to scrape https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr: RetryError[<Future at 0x10b7073b0 state=finished raised RuntimeError>]
2025-05-28 12:05:30,751 - __main__ - INFO - ✅ Initial scraping completed: 0 pages scraped
2025-05-28 12:05:30,751 - __main__ - INFO - 📊 Step 3: Extracting organizational details from scraped content
2025-05-28 12:05:30,751 - __main__ - WARNING - No scraped content available, creating minimal org details
2025-05-28 12:05:30,751 - __main__ - INFO - ✅ Organizational extraction completed
2025-05-28 12:05:30,751 - __main__ - INFO - 🔧 Step 4: Extracting plant details with OpenAI + RAG approach
2025-05-28 12:05:30,752 - __main__ - INFO - 🧠 Analyzing cached content for plant details extraction
2025-05-28 12:05:30,752 - __main__ - WARNING - No scraped content available, using RAG-only approach
2025-05-28 12:05:30,752 - __main__ - INFO - ✅ Field 'lat' extracted from cache
2025-05-28 12:05:30,752 - __main__ - INFO - ✅ Field 'long' extracted from cache
2025-05-28 12:05:30,752 - __main__ - INFO - ✅ Field 'plant_address' extracted from cache
2025-05-28 12:05:30,752 - __main__ - INFO - ✅ Field 'name' extracted from cache
2025-05-28 12:05:30,752 - __main__ - INFO - ✅ Field 'plant_type' extracted from cache
2025-05-28 12:05:30,752 - __main__ - INFO - ✅ Field 'units_id' extracted from cache
2025-05-28 12:05:30,753 - __main__ - INFO - 🎯 Starting RAG searches for 2 missing fields
2025-05-28 12:05:30,753 - __main__ - INFO - 🎯 Searching specifically for 'grid_connectivity_maps' data
2025-05-28 12:05:30,753 - __main__ - INFO - 🔍 RAG search query: 'Jhajjar Power Plant grid connection substation transmission line rated capacity classification voltage level'
2025-05-28 12:05:55,987 - __main__ - WARNING - Failed to scrape targeted URL https://herc.gov.in/writereaddata/pdf/stdtrn(3).pdf: RetryError[<Future at 0x10b706f30 state=finished raised RuntimeError>]
2025-05-28 12:13:15,373 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 12:13:15,373 - src.enhanced_extractor - INFO - Enhanced data extractor initialized with OpenAI
2025-05-28 12:13:15,379 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 12:13:15,390 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 12:13:15,390 - src.plant_details_extractor - INFO - Plant details extractor initialized with OpenAI
2025-05-28 12:13:15,390 - __main__ - INFO - 🔍 Starting OpenAI RAG extraction for: Jhajjar Power Plant
2025-05-28 12:13:15,390 - __main__ - INFO - 📡 Step 1: Searching for top 5 links
2025-05-28 12:13:20,863 - __main__ - INFO - 🌐 Step 2: Scraping 5 top results
2025-05-28 12:13:20,863 - __main__ - INFO - Scraping 1/5: https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html
2025-05-28 12:13:28,867 - __main__ - WARNING - Failed to scrape https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html: RetryError[<Future at 0x112c484d0 state=finished raised RuntimeError>]
2025-05-28 12:13:28,868 - __main__ - INFO - Scraping 2/5: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 12:13:36,871 - __main__ - WARNING - Failed to scrape https://en.wikipedia.org/wiki/Jhajjar_Power_Station: RetryError[<Future at 0x112c6a270 state=finished raised RuntimeError>]
2025-05-28 12:13:36,872 - __main__ - INFO - Scraping 3/5: https://www.gem.wiki/Mahatma_Gandhi_power_station
2025-05-28 12:13:44,875 - __main__ - WARNING - Failed to scrape https://www.gem.wiki/Mahatma_Gandhi_power_station: RetryError[<Future at 0x112c491c0 state=finished raised RuntimeError>]
2025-05-28 12:13:44,875 - __main__ - INFO - Scraping 4/5: https://apraava.com/projects/jhajjar-power-plant
2025-05-28 12:13:52,878 - __main__ - WARNING - Failed to scrape https://apraava.com/projects/jhajjar-power-plant: RetryError[<Future at 0x112e7a900 state=finished raised RuntimeError>]
2025-05-28 12:13:52,879 - __main__ - INFO - Scraping 5/5: https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr
2025-05-28 12:14:00,883 - __main__ - WARNING - Failed to scrape https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr: RetryError[<Future at 0x1138a6c90 state=finished raised RuntimeError>]
2025-05-28 12:14:00,884 - __main__ - INFO - ✅ Initial scraping completed: 0 pages scraped
2025-05-28 12:14:00,884 - __main__ - INFO - 📊 Step 3: Extracting organizational details from scraped content
2025-05-28 12:14:00,884 - __main__ - WARNING - No scraped content available, creating minimal org details
2025-05-28 12:14:00,884 - __main__ - INFO - ✅ Organizational extraction completed
2025-05-28 12:14:00,884 - __main__ - INFO - 🔧 Step 4: Extracting plant details with OpenAI + RAG approach
2025-05-28 12:14:00,885 - __main__ - INFO - 🧠 Analyzing cached content for plant details extraction
2025-05-28 12:14:00,885 - __main__ - WARNING - No scraped content available, using RAG-only approach
2025-05-28 12:14:00,885 - __main__ - INFO - ✅ Field 'lat' extracted from cache
2025-05-28 12:14:00,885 - __main__ - INFO - ✅ Field 'long' extracted from cache
2025-05-28 12:14:00,885 - __main__ - INFO - ✅ Field 'plant_address' extracted from cache
2025-05-28 12:14:00,885 - __main__ - INFO - ✅ Field 'name' extracted from cache
2025-05-28 12:14:00,885 - __main__ - INFO - ✅ Field 'plant_type' extracted from cache
2025-05-28 12:14:00,885 - __main__ - INFO - ✅ Field 'units_id' extracted from cache
2025-05-28 12:14:00,885 - __main__ - INFO - 🎯 Starting RAG searches for 2 missing fields
2025-05-28 12:14:00,885 - __main__ - INFO - 🎯 Searching specifically for 'grid_connectivity_maps' data
2025-05-28 12:14:00,885 - __main__ - INFO - 🔍 RAG search query: 'Jhajjar Power Plant grid connection substation transmission line rated capacity classification voltage level'
2025-05-28 12:14:10,893 - __main__ - WARNING - Failed to scrape targeted URL https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf: RetryError[<Future at 0x1138a7140 state=finished raised RuntimeError>]
2025-05-28 12:14:18,898 - __main__ - WARNING - Failed to scrape targeted URL https://herc.gov.in/WriteReadData/Orders/O20161006.pdf: RetryError[<Future at 0x1138a7680 state=finished raised RuntimeError>]
2025-05-28 12:14:26,903 - __main__ - WARNING - Failed to scrape targeted URL http://www.gajendrahaldea.in/pdf/Jhajjar-Transmission-MSS.pdf: RetryError[<Future at 0x1138a77a0 state=finished raised RuntimeError>]
2025-05-28 12:14:26,905 - __main__ - INFO - 🧠 Using knowledge-based fallback for 'grid_connectivity_maps'
2025-05-28 12:14:26,905 - __main__ - INFO - 💡 Used knowledge-based fallback for 'grid_connectivity_maps': [{'details': [{'substation_name': 'Jhajjar Substation', 'substation_type': '400 kV transmission substation', 'capacity': '1320 MW', 'latitude': '28.607111', 'longitude': '76.6565', 'projects': [{'distance': '0 km'}]}]}]
2025-05-28 12:14:26,905 - __main__ - INFO - 🎯 Searching specifically for 'ppa_details' data
2025-05-28 12:14:26,906 - __main__ - INFO - 🔍 RAG search query: 'Jhajjar Power Plant power purchase agreement PPA contract capacity commencement termination date entity procuring power'
2025-05-28 12:15:15,652 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 12:15:15,652 - src.enhanced_extractor - INFO - Enhanced data extractor initialized with OpenAI
2025-05-28 12:15:15,658 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 12:15:15,678 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 12:15:15,678 - src.plant_details_extractor - INFO - Plant details extractor initialized with OpenAI
2025-05-28 12:15:15,678 - __main__ - INFO - 🔍 Starting OpenAI RAG extraction for: Jhajjar Power Plant
2025-05-28 12:15:15,678 - __main__ - INFO - 📡 Step 1: Searching for top 5 links
2025-05-28 12:15:46,683 - src.serp_client - ERROR - Unexpected error in SERP API search: 
2025-05-28 12:16:21,679 - src.serp_client - ERROR - Unexpected error in SERP API search: 
2025-05-28 12:16:56,680 - src.serp_client - ERROR - Unexpected error in SERP API search: 
2025-05-28 12:16:56,682 - __main__ - ERROR - Error in OpenAI RAG extraction: RetryError[<Future at 0x10858c080 state=finished raised TimeoutError>]
Traceback (most recent call last):
  File "/Users/<USER>/miniconda3/lib/python3.12/site-packages/tenacity/_asyncio.py", line 50, in __call__
    result = await fn(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/clem_transition/src/serp_client.py", line 65, in search
    async with self.session.get(self.base_url, params=params) as response:
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/lib/python3.12/site-packages/aiohttp/client.py", line 1187, in __aenter__
    self._resp = await self._coro
                 ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/lib/python3.12/site-packages/aiohttp/client.py", line 601, in _request
    await resp.start(conn)
  File "/Users/<USER>/miniconda3/lib/python3.12/site-packages/aiohttp/client_reqrep.py", line 960, in start
    with self._timer:
         ^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/lib/python3.12/site-packages/aiohttp/helpers.py", line 735, in __exit__
    raise asyncio.TimeoutError from None
TimeoutError

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Documents/clem_transition/run_openai_rag_pipeline.py", line 88, in extract_plant_data_with_rag
    search_results = await serp_client.search(f"{plant_name} power plant", num_results=5)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/lib/python3.12/site-packages/tenacity/_asyncio.py", line 88, in async_wrapped
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/lib/python3.12/site-packages/tenacity/_asyncio.py", line 47, in __call__
    do = self.iter(retry_state=retry_state)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/miniconda3/lib/python3.12/site-packages/tenacity/__init__.py", line 326, in iter
    raise retry_exc from fut.exception()
tenacity.RetryError: RetryError[<Future at 0x10858c080 state=finished raised TimeoutError>]
2025-05-28 12:16:56,694 - src.simple_pipeline - INFO - 📈 Extraction info saved to jhajjar_extraction_info_openai_rag_20250528_121656.json
2025-05-28 12:23:36,422 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 12:23:36,422 - src.enhanced_extractor - INFO - Enhanced data extractor initialized with OpenAI
2025-05-28 12:23:36,430 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 12:23:36,442 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 12:23:36,442 - src.plant_details_extractor - INFO - Plant details extractor initialized with OpenAI
2025-05-28 12:23:36,442 - __main__ - INFO - 🔍 Starting OpenAI RAG extraction for: Jhajjar Power Plant
2025-05-28 12:23:36,442 - __main__ - INFO - 📡 Step 1: Searching for top 5 links
2025-05-28 12:23:40,903 - __main__ - INFO - 🌐 Step 2: Scraping 5 top results
2025-05-28 12:23:40,903 - __main__ - INFO - Scraping 1/5: https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html
2025-05-28 12:23:48,907 - __main__ - WARNING - Failed to scrape https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html: RetryError[<Future at 0x10b57f020 state=finished raised RuntimeError>]
2025-05-28 12:23:48,907 - __main__ - INFO - Scraping 2/5: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 12:26:27,007 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 12:26:27,008 - src.enhanced_extractor - INFO - Enhanced data extractor initialized with OpenAI
2025-05-28 12:26:27,013 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 12:26:27,025 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 12:26:27,025 - src.plant_details_extractor - INFO - Plant details extractor initialized with OpenAI
2025-05-28 12:26:27,025 - __main__ - INFO - 🔍 Starting OpenAI RAG extraction for: Jhajjar Power Plant
2025-05-28 12:26:27,025 - __main__ - INFO - 📡 Step 1: Searching for top 5 links
2025-05-28 12:26:38,184 - __main__ - INFO - 🌐 Step 2: Scraping 5 top results
2025-05-28 12:26:38,184 - __main__ - INFO - Scraping 1/5: https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html
2025-05-28 12:26:40,453 - __main__ - INFO - Successfully scraped https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html (516 chars)
2025-05-28 12:26:41,454 - __main__ - INFO - Scraping 2/5: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 12:26:43,286 - __main__ - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-28 12:26:44,288 - __main__ - INFO - Scraping 3/5: https://www.gem.wiki/Mahatma_Gandhi_power_station
2025-05-28 12:26:45,523 - __main__ - INFO - Successfully scraped https://www.gem.wiki/Mahatma_Gandhi_power_station (7880 chars)
2025-05-28 12:26:46,524 - __main__ - INFO - Scraping 4/5: https://apraava.com/projects/jhajjar-power-plant
2025-05-28 12:26:48,450 - __main__ - INFO - Successfully scraped https://apraava.com/projects/jhajjar-power-plant (791 chars)
2025-05-28 12:26:49,451 - __main__ - INFO - Scraping 5/5: https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr
2025-05-28 12:26:52,241 - __main__ - INFO - Successfully scraped https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr (3864 chars)
2025-05-28 12:26:53,243 - __main__ - INFO - ✅ Initial scraping completed: 5 pages scraped
2025-05-28 12:26:53,244 - __main__ - INFO - 📊 Step 3: Extracting organizational details from scraped content
2025-05-28 12:26:53,244 - __main__ - ERROR - Error in OpenAI RAG extraction: 'AdaptiveExtractor' object has no attribute 'extract_org_details'
Traceback (most recent call last):
  File "/Users/<USER>/Documents/clem_transition/run_openai_rag_pipeline.py", line 118, in extract_plant_data_with_rag
    org_details = await self.enhanced_extractor.extract_org_details(scraped_contents, plant_name)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AdaptiveExtractor' object has no attribute 'extract_org_details'
2025-05-28 12:26:53,249 - src.simple_pipeline - INFO - 📈 Extraction info saved to jhajjar_extraction_info_openai_rag_20250528_122653.json
2025-05-28 12:27:54,247 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 12:27:54,247 - src.enhanced_extractor - INFO - Enhanced data extractor initialized with OpenAI
2025-05-28 12:27:54,253 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 12:27:54,264 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 12:27:54,264 - src.plant_details_extractor - INFO - Plant details extractor initialized with OpenAI
2025-05-28 12:27:54,264 - __main__ - INFO - 🔍 Starting OpenAI RAG extraction for: Jhajjar Power Plant
2025-05-28 12:27:54,264 - __main__ - INFO - 📡 Step 1: Searching for top 5 links
2025-05-28 12:28:25,174 - __main__ - INFO - 🌐 Step 2: Scraping 5 top results
2025-05-28 12:28:25,175 - __main__ - INFO - Scraping 1/5: https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html
2025-05-28 12:28:26,817 - __main__ - INFO - Successfully scraped https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html (516 chars)
2025-05-28 12:28:27,818 - __main__ - INFO - Scraping 2/5: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 12:28:28,731 - __main__ - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-28 12:28:29,732 - __main__ - INFO - Scraping 3/5: https://www.gem.wiki/Mahatma_Gandhi_power_station
2025-05-28 12:28:31,038 - __main__ - INFO - Successfully scraped https://www.gem.wiki/Mahatma_Gandhi_power_station (7880 chars)
2025-05-28 12:28:32,040 - __main__ - INFO - Scraping 4/5: https://apraava.com/projects/jhajjar-power-plant
2025-05-28 12:28:33,959 - __main__ - INFO - Successfully scraped https://apraava.com/projects/jhajjar-power-plant (791 chars)
2025-05-28 12:28:34,960 - __main__ - INFO - Scraping 5/5: https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr
2025-05-28 12:28:36,286 - __main__ - INFO - Successfully scraped https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr (3864 chars)
2025-05-28 12:28:37,288 - __main__ - INFO - ✅ Initial scraping completed: 5 pages scraped
2025-05-28 12:28:37,289 - __main__ - INFO - 📊 Step 3: Extracting organizational details from scraped content
2025-05-28 12:28:37,289 - src.enhanced_extractor - INFO - Content analysis: {'total_sources': 5, 'total_content': 13541, 'avg_relevance': 0.8380000000000001, 'high_quality_sources': 1, 'source_type_diversity': 3, 'source_types': ['company_official', 'wikipedia', 'other']}
2025-05-28 12:28:37,289 - src.enhanced_extractor - INFO - Using basic extraction with validation
2025-05-28 12:28:37,289 - src.openai_client - INFO - Starting OpenAI LLM extraction for 5 content pieces
2025-05-28 12:28:38,497 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 12:28:38,506 - src.openai_client - INFO - Extracted organization_name: CLP India Private Limited (confidence: 0.90)
2025-05-28 12:28:41,544 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 12:28:41,550 - src.openai_client - INFO - Extracted cfpp_type: joint_venture (confidence: 0.60)
2025-05-28 12:28:42,939 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 12:28:42,942 - src.openai_client - INFO - Extracted country_name: India (confidence: 0.90)
2025-05-28 12:28:44,142 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 12:28:44,144 - src.openai_client - INFO - Extracted province: Haryana (confidence: 0.90)
2025-05-28 12:28:45,364 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 12:28:45,368 - src.openai_client - INFO - Extracted plants_count: 1 (confidence: 0.80)
2025-05-28 12:28:46,631 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 12:28:46,636 - src.openai_client - INFO - Extracted plant_types: ['coal'] (confidence: 0.80)
2025-05-28 12:28:48,454 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 12:28:48,458 - src.openai_client - INFO - Extracted ppa_flag: Plant (confidence: 0.70)
2025-05-28 12:28:49,876 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 12:28:49,884 - src.openai_client - INFO - Extracted currency_in: INR (confidence: 0.70)
2025-05-28 12:28:51,025 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 12:28:51,028 - src.openai_client - INFO - Extracted financial_year: 04-03 (confidence: 0.70)
2025-05-28 12:28:51,530 - __main__ - INFO - ✅ Organizational extraction completed
2025-05-28 12:28:51,531 - __main__ - INFO - 🔧 Step 4: Extracting plant details with OpenAI + RAG approach
2025-05-28 12:28:51,531 - __main__ - INFO - 🧠 Analyzing cached content for plant details extraction
2025-05-28 12:28:51,531 - src.plant_details_extractor - INFO - Starting plant details extraction for: Jhajjar Power Plant
2025-05-28 12:28:59,534 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field name: RetryError[<Future at 0x107447cb0 state=finished raised TypeError>]
2025-05-28 12:28:59,536 - src.plant_details_extractor - INFO - Extracted name: None (confidence: 0.00)
2025-05-28 12:29:08,041 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field plant_type: RetryError[<Future at 0x1070af410 state=finished raised TypeError>]
2025-05-28 12:29:08,042 - src.plant_details_extractor - INFO - Extracted plant_type: None (confidence: 0.00)
2025-05-28 12:29:16,547 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field plant_address: RetryError[<Future at 0x1070ae930 state=finished raised TypeError>]
2025-05-28 12:29:16,547 - src.plant_details_extractor - INFO - Extracted plant_address: None (confidence: 0.00)
2025-05-28 12:29:25,051 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field lat: RetryError[<Future at 0x107456630 state=finished raised TypeError>]
2025-05-28 12:29:25,052 - src.plant_details_extractor - INFO - Extracted lat: None (confidence: 0.00)
2025-05-28 12:29:33,556 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field long: RetryError[<Future at 0x107456360 state=finished raised TypeError>]
2025-05-28 12:29:33,557 - src.plant_details_extractor - INFO - Extracted long: None (confidence: 0.00)
2025-05-28 12:29:42,061 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field units_id: RetryError[<Future at 0x10745cd40 state=finished raised TypeError>]
2025-05-28 12:29:42,063 - src.plant_details_extractor - INFO - Extracted units_id: None (confidence: 0.00)
2025-05-28 12:29:42,564 - src.plant_details_extractor - ERROR - Failed to extract grid_connectivity: No prompt template found for plant field: grid_connectivity
2025-05-28 12:29:42,565 - src.plant_details_extractor - ERROR - Failed to extract ppa_details: '\n  "capacity"'
2025-05-28 12:29:42,565 - src.plant_details_extractor - INFO - Using organization name for plant name: CLP India Private Limited
2025-05-28 12:29:42,565 - src.plant_details_extractor - INFO - Derived plant_type from org data: coal
2025-05-28 12:29:42,565 - src.plant_details_extractor - INFO - Plant details extraction completed for: Jhajjar Power Plant
2025-05-28 12:29:42,565 - __main__ - INFO - ✅ Field 'name' extracted from cache
2025-05-28 12:29:42,565 - __main__ - INFO - ✅ Field 'plant_type' extracted from cache
2025-05-28 12:29:42,565 - __main__ - INFO - 🎯 Starting RAG searches for 6 missing fields
2025-05-28 12:29:42,565 - __main__ - INFO - 🎯 Searching specifically for 'lat' data
2025-05-28 12:29:42,565 - __main__ - INFO - 🔍 RAG search query: 'Jhajjar Power Plant plant's own latitude coordinate decimal degrees GPS location'
2025-05-28 12:30:03,143 - __main__ - INFO - ✅ Found targeted content for 'lat' - 3 sources
2025-05-28 12:30:03,147 - __main__ - INFO - 📝 Content found for 'lat' but RAG extraction failed
2025-05-28 12:30:03,147 - __main__ - INFO - 🎯 Searching specifically for 'long' data
2025-05-28 12:30:03,147 - __main__ - INFO - 🔍 RAG search query: 'Jhajjar Power Plant plant's own longitude coordinate decimal degrees GPS location'
2025-05-28 12:37:10,435 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 12:37:10,435 - src.enhanced_extractor - INFO - Enhanced data extractor initialized with OpenAI
2025-05-28 12:37:10,442 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 12:37:10,454 - src.openai_client - INFO - Initialized OpenAI client with model: gpt-4o-mini
2025-05-28 12:37:10,454 - src.plant_details_extractor - INFO - Plant details extractor initialized with OpenAI
2025-05-28 12:37:10,454 - __main__ - INFO - 🔍 Starting OpenAI RAG extraction for: Jhajjar Power Plant
2025-05-28 12:37:10,454 - __main__ - INFO - 📡 Step 1: Searching for top 5 links
2025-05-28 12:37:14,942 - __main__ - INFO - 🌐 Step 2: Scraping 5 top results
2025-05-28 12:37:14,942 - __main__ - INFO - Scraping 1/5: https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html
2025-05-28 12:37:18,359 - __main__ - INFO - Successfully scraped https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html (516 chars)
2025-05-28 12:37:19,360 - __main__ - INFO - Scraping 2/5: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 12:37:20,215 - __main__ - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-28 12:37:21,216 - __main__ - INFO - Scraping 3/5: https://www.gem.wiki/Mahatma_Gandhi_power_station
2025-05-28 12:37:22,707 - __main__ - INFO - Successfully scraped https://www.gem.wiki/Mahatma_Gandhi_power_station (7880 chars)
2025-05-28 12:37:23,708 - __main__ - INFO - Scraping 4/5: https://apraava.com/projects/jhajjar-power-plant
2025-05-28 12:37:28,832 - __main__ - INFO - Successfully scraped https://apraava.com/projects/jhajjar-power-plant (791 chars)
2025-05-28 12:37:29,836 - __main__ - INFO - Scraping 5/5: https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr
2025-05-28 12:37:31,749 - __main__ - INFO - Successfully scraped https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr (3864 chars)
2025-05-28 12:37:32,752 - __main__ - INFO - ✅ Initial scraping completed: 5 pages scraped
2025-05-28 12:37:32,752 - __main__ - INFO - 📊 Step 3: Extracting organizational details from scraped content
2025-05-28 12:37:32,753 - src.enhanced_extractor - INFO - Content analysis: {'total_sources': 5, 'total_content': 13541, 'avg_relevance': 0.8380000000000001, 'high_quality_sources': 1, 'source_type_diversity': 3, 'source_types': ['other', 'company_official', 'wikipedia']}
2025-05-28 12:37:32,753 - src.enhanced_extractor - INFO - Using basic extraction with validation
2025-05-28 12:37:32,753 - src.openai_client - INFO - Starting OpenAI LLM extraction for 5 content pieces
2025-05-28 12:37:34,617 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 12:37:34,624 - src.openai_client - INFO - Extracted organization_name: CLP India Private Limited (confidence: 0.90)
2025-05-28 12:37:36,461 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 12:37:36,463 - src.openai_client - INFO - Extracted cfpp_type: joint_venture (confidence: 0.60)
2025-05-28 12:37:37,685 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 12:37:37,690 - src.openai_client - INFO - Extracted country_name: India (confidence: 0.90)
2025-05-28 12:37:39,621 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 12:37:39,628 - src.openai_client - INFO - Extracted province: Haryana (confidence: 0.90)
2025-05-28 12:37:41,773 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 12:37:41,776 - src.openai_client - INFO - Extracted plants_count: 1 (confidence: 0.80)
2025-05-28 12:37:49,557 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 12:37:49,561 - src.openai_client - INFO - Extracted plant_types: ['coal'] (confidence: 0.80)
2025-05-28 12:37:51,853 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 12:37:51,856 - src.openai_client - INFO - Extracted ppa_flag: Plant (confidence: 0.70)
2025-05-28 12:38:07,816 - openai._base_client - INFO - Retrying request to /chat/completions in 0.896225 seconds
2025-05-28 12:38:38,600 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 12:38:38,611 - src.openai_client - INFO - Extracted currency_in: INR (confidence: 0.70)
2025-05-28 12:38:59,042 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 12:38:59,043 - src.openai_client - INFO - Extracted financial_year: 04-03 (confidence: 0.70)
2025-05-28 12:38:59,546 - __main__ - INFO - ✅ Organizational extraction completed
2025-05-28 12:38:59,546 - __main__ - INFO - 🔧 Step 4: Extracting plant details with OpenAI + RAG approach
2025-05-28 12:38:59,546 - __main__ - INFO - 🧠 Analyzing cached content for plant details extraction
2025-05-28 12:38:59,546 - src.plant_details_extractor - INFO - Starting plant details extraction for: Jhajjar Power Plant
2025-05-28 12:39:07,551 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field name: RetryError[<Future at 0x12764e540 state=finished raised TypeError>]
2025-05-28 12:39:07,551 - src.plant_details_extractor - INFO - Extracted name: None (confidence: 0.00)
2025-05-28 12:39:16,056 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field plant_type: RetryError[<Future at 0x12764c530 state=finished raised TypeError>]
2025-05-28 12:39:16,057 - src.plant_details_extractor - INFO - Extracted plant_type: None (confidence: 0.00)
2025-05-28 12:39:24,561 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field plant_address: RetryError[<Future at 0x12764c830 state=finished raised TypeError>]
2025-05-28 12:39:24,563 - src.plant_details_extractor - INFO - Extracted plant_address: None (confidence: 0.00)
2025-05-28 12:39:33,070 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field lat: RetryError[<Future at 0x1276590a0 state=finished raised TypeError>]
2025-05-28 12:39:33,071 - src.plant_details_extractor - INFO - Extracted lat: None (confidence: 0.00)
2025-05-28 12:39:41,577 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field long: RetryError[<Future at 0x12765ac30 state=finished raised TypeError>]
2025-05-28 12:39:41,577 - src.plant_details_extractor - INFO - Extracted long: None (confidence: 0.00)
2025-05-28 12:39:50,082 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field units_id: RetryError[<Future at 0x12765ee10 state=finished raised TypeError>]
2025-05-28 12:39:50,083 - src.plant_details_extractor - INFO - Extracted units_id: None (confidence: 0.00)
2025-05-28 12:39:50,584 - src.plant_details_extractor - ERROR - Failed to extract grid_connectivity: No prompt template found for plant field: grid_connectivity
2025-05-28 12:39:50,585 - src.plant_details_extractor - ERROR - Failed to extract ppa_details: '\n  "capacity"'
2025-05-28 12:39:50,585 - src.plant_details_extractor - INFO - Using organization name for plant name: CLP India Private Limited
2025-05-28 12:39:50,585 - src.plant_details_extractor - INFO - Derived plant_type from org data: coal
2025-05-28 12:39:50,585 - src.plant_details_extractor - INFO - Plant details extraction completed for: Jhajjar Power Plant
2025-05-28 12:39:50,585 - __main__ - INFO - ✅ Field 'name' extracted from cache
2025-05-28 12:39:50,585 - __main__ - INFO - ✅ Field 'plant_type' extracted from cache
2025-05-28 12:39:50,585 - __main__ - INFO - 🎯 Starting RAG searches for 6 missing fields
2025-05-28 12:39:50,585 - __main__ - INFO - 🎯 Searching specifically for 'lat' data
2025-05-28 12:39:50,585 - __main__ - INFO - 🔍 RAG search query: 'Jhajjar Power Plant plant's own latitude coordinate decimal degrees GPS location'
2025-05-28 12:40:20,322 - __main__ - INFO - ✅ Found targeted content for 'lat' - 3 sources
2025-05-28 12:40:20,326 - __main__ - INFO - 🎉 Successfully extracted 'lat' using RAG: 28.607111
2025-05-28 12:40:20,326 - __main__ - INFO - 🎯 Searching specifically for 'long' data
2025-05-28 12:40:20,326 - __main__ - INFO - 🔍 RAG search query: 'Jhajjar Power Plant plant's own longitude coordinate decimal degrees GPS location'
2025-05-28 12:40:43,105 - __main__ - INFO - ✅ Found targeted content for 'long' - 3 sources
2025-05-28 12:40:43,107 - __main__ - INFO - 🎉 Successfully extracted 'long' using RAG: 76.656914
2025-05-28 12:40:43,108 - __main__ - INFO - 🎯 Searching specifically for 'plant_address' data
2025-05-28 12:40:43,108 - __main__ - INFO - 🔍 RAG search query: 'Jhajjar Power Plant district or city state country location'
2025-05-28 12:40:59,224 - __main__ - INFO - ✅ Found targeted content for 'plant_address' - 3 sources
2025-05-28 12:40:59,225 - __main__ - INFO - 🎉 Successfully extracted 'plant_address' using RAG: jharli
village in jhajjar district of haryana
2025-05-28 12:40:59,225 - __main__ - INFO - 🎯 Searching specifically for 'grid_connectivity_maps' data
2025-05-28 12:40:59,226 - __main__ - INFO - 🔍 RAG search query: 'Jhajjar Power Plant grid connection substation transmission line rated capacity classification voltage level'
2025-05-28 12:41:23,392 - src.pdf_processor - INFO - Successfully extracted 112384 chars from PDF using pdfplumber: http://www.gajendrahaldea.in/pdf/Jhajjar-Transmission-MSS.pdf
2025-05-28 12:41:23,395 - src.scraper_client - INFO - Successfully processed PDF: http://www.gajendrahaldea.in/pdf/Jhajjar-Transmission-MSS.pdf (50003 chars, score: 1.00)
2025-05-28 12:42:21,238 - src.scraper_client - WARNING - Failed to scrape https://cea.nic.in/wp-content/uploads/psp___a_ii/2023/03/Manual_on_Transmission_Planning_Criteria_2023.pdf: HTTP 500
2025-05-28 12:42:29,013 - readability.readability - INFO - ruthless removal did not work. 
2025-05-28 12:42:30,082 - __main__ - INFO - ✅ Found targeted content for 'grid_connectivity_maps' - 2 sources
2025-05-28 12:42:30,083 - __main__ - INFO - 🎉 Successfully extracted 'grid_connectivity_maps' using RAG: [{'details': [{'substation_name': 'automation system (sas)', 'substation_type': 'transmission', 'capacity': '', 'latitude': '', 'longitude': '', 'projects': []}]}]
2025-05-28 12:42:30,083 - __main__ - INFO - 🎯 Searching specifically for 'ppa_details' data
2025-05-28 12:42:30,083 - __main__ - INFO - 🔍 RAG search query: 'Jhajjar Power Plant power purchase agreement PPA contract capacity commencement termination date entity procuring power'
2025-05-28 12:43:11,655 - src.pdf_processor - INFO - Successfully extracted 111606 chars from PDF using pdfplumber: https://cercind.gov.in/2023/orders/637-MP-2020.pdf
2025-05-28 12:43:11,658 - src.scraper_client - INFO - Successfully processed PDF: https://cercind.gov.in/2023/orders/637-MP-2020.pdf (50003 chars, score: 1.00)
2025-05-28 12:43:16,978 - __main__ - INFO - ✅ Found targeted content for 'ppa_details' - 2 sources
2025-05-28 12:43:16,978 - __main__ - INFO - 🎉 Successfully extracted 'ppa_details' using RAG: [{'capacity': '', 'capacity_unit': 'MW', 'start_date': '', 'end_date': '', 'tenure': None, 'tenure_type': 'Years', 'respondents': [{'name': '', 'capacity': '', 'currency': 'INR', 'price': '', 'price_unit': 'INR/MWh'}]}]
2025-05-28 12:43:16,979 - __main__ - INFO - 🎯 Searching specifically for 'units_id' data
2025-05-28 12:43:16,979 - __main__ - INFO - 🔍 RAG search query: 'Jhajjar Power Plant integers from 1 to number of units at plant generation units'
2025-05-28 12:43:49,724 - src.pdf_processor - INFO - Successfully extracted 89455 chars from PDF using pdfplumber: https://www.apraava.com/getmedia/8932fe7b-f82d-4a82-ab8c-32843bd9de2e/2020-21.pdf
2025-05-28 12:43:49,727 - src.scraper_client - INFO - Successfully processed PDF: https://www.apraava.com/getmedia/8932fe7b-f82d-4a82-ab8c-32843bd9de2e/2020-21.pdf (50003 chars, score: 1.00)
2025-05-28 12:43:56,302 - __main__ - INFO - ✅ Found targeted content for 'units_id' - 3 sources
2025-05-28 12:43:56,308 - __main__ - INFO - 🎉 Successfully extracted 'units_id' using RAG: [1, 2]
2025-05-28 12:43:56,308 - __main__ - INFO - ✅ PlantDetails object created successfully
2025-05-28 12:43:56,308 - __main__ - INFO - 🎉 OpenAI RAG extraction completed for: Jhajjar Power Plant
2025-05-28 12:43:56,308 - __main__ - INFO - ⏱️  Total time: 405.9s
2025-05-28 12:43:56,309 - __main__ - INFO - 💾 Cache efficiency: 2 fields from cache, 6 targeted searches
2025-05-28 12:43:56,312 - src.simple_pipeline - INFO - 📊 Organizational results saved to jhajjar_org_openai_rag_20250528_124356.json
2025-05-28 12:43:56,312 - src.simple_pipeline - INFO - 🔧 Plant details results saved to jhajjar_plant_openai_rag_20250528_124356.json
2025-05-28 12:43:56,312 - src.simple_pipeline - INFO - 📈 Extraction info saved to jhajjar_extraction_info_openai_rag_20250528_124356.json
