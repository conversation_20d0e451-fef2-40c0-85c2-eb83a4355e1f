# Power Plant Data Extraction Pipeline

A comprehensive web-based data retrieval pipeline for extracting both **organizational** and **technical plant details** about power plants using SERP API, Scraper API, and Groq LLM.

## 🎯 Dual Extraction Objectives

### 📊 Organizational Details (9 fields)
Extract key organizational information for power plants:

- `cfpp_type`: Power plant classification/type
- `country_name`: Country location
- `currency_in`: Financial reporting currency
- `financial_year`: Fiscal year period in MM-MM format (e.g., "04-03" for April to March)
- `organization_name`: Owner/operator company name
- `plants_count`: Total facilities owned by organization
- `plant_types`: Technologies operated by organization
- `ppa_flag`: Power Purchase Agreement existence
- `province`: Sub-national region/state

### 🔧 Plant Technical Details (9 fields)
Extract comprehensive technical information for individual plants:

- `name`: Official power plant name
- `plant_type`: Technology/fuel type (coal, nuclear, solar, etc.)
- `plant_address`: District or city, State, Country location
- `lat` / `long`: Plant's own coordinates in decimal degrees
- `plant_id`: Unique system identifier
- `units_id`: Integers from 1 to number of units at plant
- `grid_connectivity_maps`: Substation and grid connection details
- `ppa_details`: Power Purchase Agreement contract information

## 🏗️ Architecture

### Phase-by-Phase Implementation

**Phase 1: Project Setup** ✅
- Dependencies and environment configuration
- Data models and validation schemas
- Configuration management

**Phase 2: Search Discovery** ✅
- SERP API integration for multi-stage search
- Query template system for comprehensive coverage
- Source type identification and prioritization

**Phase 3: Content Extraction** ✅
- Scraper API integration for web content extraction
- Content preprocessing and cleaning
- Relevance scoring and filtering

**Phase 4: LLM Processing** ✅
- Groq Llama 3.3 70b integration for data extraction
- Field-specific extraction prompts with context formatting
- Confidence scoring and response validation
- Intelligent response processing for each field type

**Phase 5: Data Validation** ✅
- Cross-source validation with consensus algorithms
- Multi-strategy extraction (combined, individual, grouped)
- Adaptive extraction based on content quality
- Advanced fallback strategies and error handling

## 🚀 Quick Start

### 1. Setup

```bash
# Clone and setup
python setup.py

# Edit .env file with your API keys
cp .env.example .env
# Add your API keys to .env
```

### 2. Required API Keys

- **SERP API**: Get from [serpapi.com](https://serpapi.com)
- **Scraper API**: Get from [scraperapi.com](https://scraperapi.com)
- **Groq API**: Get from [groq.com](https://groq.com)

### 3. Test the Pipeline

```bash
# Run component tests
python test_pipeline.py

# Test plant details pipeline
python test_plant_details.py

# Run organizational details only
python -m src.pipeline

# Run unified pipeline (both organizational + technical)
python -m src.unified_pipeline

# Run smart cache-optimized pipeline (RECOMMENDED)
python -m src.smart_unified_pipeline

# Run comprehensive demos
python demo.py                    # Organizational details demo
python demo_plant_details.py      # Plant technical details demo
python demo_smart_pipeline.py     # Smart cache-optimized demo

# Test Phase 5 validation features
python test_phase5.py

# Test financial year functionality
python test_financial_year_simple.py
python demo_financial_year.py
```

## 📁 Project Structure

```
├── src/
│   ├── __init__.py
│   ├── models.py                    # Data models and schemas (org + plant)
│   ├── config.py                    # Configuration management
│   ├── serp_client.py              # SERP API integration
│   ├── scraper_client.py           # Web scraping client
│   ├── groq_client.py              # Groq LLM integration
│   ├── validation.py               # Data validation and consensus
│   ├── enhanced_extractor.py       # Multi-strategy extraction (org)
│   ├── plant_details_extractor.py  # Plant technical details extractor
│   ├── pipeline.py                 # Organizational details pipeline
│   └── unified_pipeline.py         # Unified pipeline (org + plant)
├── org_details.json                # Organizational output schema
├── plant_details.json              # Plant technical output schema
├── requirements.txt                # Dependencies
├── .env.example                    # Environment template
├── setup.py                        # Setup script
├── test_pipeline.py                # Basic test suite
├── test_plant_details.py           # Plant details test suite
├── test_phase5.py                  # Phase 5 validation tests
├── demo.py                         # Organizational details demo
├── demo_plant_details.py           # Plant technical details demo
└── README.md
```

## 🔧 Configuration

### Search Strategy

The pipeline uses a multi-stage search approach:

1. **Basic Discovery**: Find general information about the plant
2. **Organizational**: Identify owner/operator details
3. **Technical Details**: Extract plant type and specifications
4. **Location Details**: Determine geographic information
5. **PPA Details**: Check for power purchase agreements
6. **Portfolio Details**: Find organization's other facilities

### Source Prioritization

Sources are ranked by reliability:

1. **Company Official** (Priority 10): Corporate websites, investor relations
2. **Regulatory Filing** (Priority 9): SEC filings, energy commission reports
3. **Government Database** (Priority 8): EIA, energy authorities
4. **Industry Report** (Priority 7): Platts, trade publications
5. **News Article** (Priority 6): Reuters, Bloomberg
6. **Wikipedia** (Priority 5): General encyclopedias

### Content Processing

- **Preprocessing**: HTML cleaning, text extraction, relevance scoring
- **Filtering**: Minimum content length, maximum size limits
- **Deduplication**: Remove duplicate URLs across search categories

## 📊 Current Implementation Status

### ✅ Completed (Phase 1-5)

- [x] Project structure and dependencies
- [x] Data models with Pydantic validation
- [x] Configuration management system
- [x] SERP API client with multi-stage search
- [x] Web scraper with content preprocessing
- [x] Pipeline orchestration framework
- [x] Groq LLM integration for data extraction
- [x] Field-specific extraction prompts
- [x] Confidence scoring system
- [x] Response validation and processing
- [x] Cross-source validation with consensus algorithms
- [x] Multi-strategy extraction (combined, individual, grouped)
- [x] Adaptive extraction based on content quality
- [x] Advanced fallback strategies and error handling
- [x] Comprehensive test suite and demo scripts

### 🚀 Ready for Production

The pipeline now includes all core features for reliable power plant data extraction with:
- **Multi-source validation**: Consensus from multiple extraction strategies
- **Adaptive processing**: Automatically adjusts strategy based on content quality
- **Robust error handling**: Multiple fallback mechanisms for reliable operation

### 📋 Future Enhancements

1. **Performance Optimization**
   - Add caching for search results and extractions
   - Implement parallel processing for multiple plants
   - Optimize API usage and rate limiting
   - Add result persistence and incremental updates

2. **Advanced Features**
   - Add PDF processing capabilities with RAG
   - Implement multi-language support
   - Add real-time data monitoring and updates
   - Create web interface for pipeline management

3. **Enterprise Features**
   - Add database integration for result storage
   - Implement batch processing for large datasets
   - Add monitoring and alerting capabilities
   - Create REST API for integration with other systems

## 🧪 Testing

### Component Tests
```bash
python test_pipeline.py
```

Tests individual components:
- Model validation
- Configuration loading
- API client initialization
- Search query generation

### Integration Tests

Test with real power plants:
- Vogtle Nuclear Power Plant
- Palo Verde Nuclear Generating Station
- Hoover Dam

## 📈 Performance Metrics

- **Search Coverage**: 5 search categories, 10+ queries per plant
- **Content Volume**: Up to 50KB per scraped page
- **Source Diversity**: 6 source types with priority weighting
- **Rate Limiting**: 1-2 second delays between API calls

## 🔍 Usage Examples

### Organizational Details Only
```python
from src.pipeline import PowerPlantDataPipeline

# Initialize pipeline
pipeline = PowerPlantDataPipeline()

# Extract organizational data
org_details = await pipeline.extract_organizational_details("Vogtle Nuclear Power Plant")

# Save results
await pipeline.save_results(org_details, "vogtle_org.json")
```

### Plant Technical Details Only
```python
from src.unified_pipeline import UnifiedPowerPlantPipeline

# Initialize unified pipeline
pipeline = UnifiedPowerPlantPipeline()

# Extract technical details only
org_details, plant_details = await pipeline.extract_complete_plant_data(
    "Vogtle Nuclear Power Plant",
    extract_organizational=False,
    extract_technical=True
)

# Save results
await pipeline.save_results(plant_details=plant_details)
```

### Complete Extraction (Both Organizational + Technical)
```python
from src.unified_pipeline import UnifiedPowerPlantPipeline

# Initialize unified pipeline
pipeline = UnifiedPowerPlantPipeline()

# Extract both organizational and technical data
org_details, plant_details = await pipeline.extract_complete_plant_data(
    "Vogtle Nuclear Power Plant",
    extract_organizational=True,
    extract_technical=True
)

# Save both results
await pipeline.save_results(
    org_details=org_details,
    plant_details=plant_details,
    org_output_path="vogtle_org.json",
    plant_output_path="vogtle_plant.json"
)
```

### 🧠 Smart Cache-Optimized Extraction (Recommended)
```python
from src.smart_unified_pipeline import SmartUnifiedPowerPlantPipeline

# Initialize smart pipeline with caching
pipeline = SmartUnifiedPowerPlantPipeline()

# Smart extraction with cache optimization
org_details, plant_details, cache_info = await pipeline.extract_complete_plant_data_smart(
    "Vogtle Nuclear Power Plant",
    extract_organizational=True,
    extract_technical=True,
    use_cache_optimization=True  # Reuses scraped content intelligently
)

# Save results with cache analysis
await pipeline.save_results(
    org_details=org_details,
    plant_details=plant_details,
    cache_info=cache_info
)

# Check cache efficiency
print(f"Cache hits: {len(cache_info['cache_hit_fields'])}")
print(f"Additional searches: {len(cache_info['additional_searches'])}")
```

## 📝 Notes

- **Phase 5 Complete**: Full implementation with enhanced validation
- **Multi-Strategy Extraction**: Combined, individual, and grouped source processing
- **Adaptive Processing**: Automatically selects best strategy based on content quality
- **Cross-Source Validation**: Consensus algorithms for reliable data extraction
- **Robust Error Handling**: Multiple fallback mechanisms for production reliability
- **Production Ready**: Comprehensive testing and validation framework

## 🤝 Contributing

This is a phase-by-phase implementation. Current status:
1. ✅ **Phase 1-5 Complete**: Full pipeline with enhanced validation
2. 🚀 **Production Ready**: Reliable multi-source data extraction
3. 📈 **Future**: Performance optimization and enterprise features
