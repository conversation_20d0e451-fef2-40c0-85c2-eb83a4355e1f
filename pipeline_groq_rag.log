2025-05-28 11:02:32,967 - src.enhanced_extractor - INFO - Enhanced data extractor initialized with Groq
2025-05-28 11:02:32,978 - src.plant_details_extractor - INFO - Plant details extractor initialized with Groq
2025-05-28 11:02:32,978 - src.simple_pipeline - INFO - 🔍 Starting simplified extraction for: Jhajjar Power Plant
2025-05-28 11:02:32,978 - src.simple_pipeline - INFO - 📡 Step 1: Searching for 'Jhajjar Power Plant' - getting top 5 links
2025-05-28 11:02:37,478 - src.simple_pipeline - INFO - 🌐 Step 2: Scraping 5 top results
2025-05-28 11:02:37,479 - src.simple_pipeline - INFO - Scraping 1/5: https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html
2025-05-28 11:02:38,765 - src.simple_pipeline - INFO - Successfully scraped https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html (516 chars)
2025-05-28 11:02:39,766 - src.simple_pipeline - INFO - Scraping 2/5: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 11:02:40,286 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-28 11:02:41,288 - src.simple_pipeline - INFO - Scraping 3/5: https://apraava.com/projects/jhajjar-power-plant
2025-05-28 11:02:43,911 - src.simple_pipeline - INFO - Successfully scraped https://apraava.com/projects/jhajjar-power-plant (791 chars)
2025-05-28 11:02:44,912 - src.simple_pipeline - INFO - Scraping 4/5: https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr
2025-05-28 11:02:59,036 - src.simple_pipeline - INFO - Successfully scraped https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr (3864 chars)
2025-05-28 11:03:00,037 - src.simple_pipeline - INFO - Scraping 5/5: https://m.economictimes.com/industry/energy/power/jindal-power-emerges-frontrunner-to-acquire-apraavas-haryana-project/articleshow/*********.cms
2025-05-28 11:03:06,762 - src.simple_pipeline - INFO - Successfully scraped https://m.economictimes.com/industry/energy/power/jindal-power-emerges-frontrunner-to-acquire-apraavas-haryana-project/articleshow/*********.cms (2246 chars)
2025-05-28 11:03:07,765 - src.simple_pipeline - INFO - ✅ Initial scraping completed: 5 pages scraped
2025-05-28 11:03:07,765 - src.simple_pipeline - INFO - 📊 Step 3: Extracting organizational details from scraped content
2025-05-28 11:03:07,765 - src.enhanced_extractor - INFO - Content analysis: {'total_sources': 5, 'total_content': 7907, 'avg_relevance': 0.8380000000000001, 'high_quality_sources': 1, 'source_type_diversity': 3, 'source_types': ['other', 'wikipedia', 'company_official']}
2025-05-28 11:03:07,765 - src.enhanced_extractor - INFO - Using basic extraction with validation
2025-05-28 11:03:07,766 - src.groq_client - INFO - Starting LLM extraction for 5 content pieces
2025-05-28 11:03:08,493 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 11:03:08,766 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 11:03:08,768 - src.groq_client - INFO - Extracted cfpp_type: joint_venture (confidence: 0.90)
2025-05-28 11:03:10,262 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 11:03:10,263 - src.groq_client - INFO - Extracted country_name: India (confidence: 0.80)
2025-05-28 11:03:11,222 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 11:03:11,226 - src.groq_client - INFO - Extracted province: Haryana (confidence: 0.70)
2025-05-28 11:03:12,220 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 11:03:12,223 - src.groq_client - INFO - Extracted plants_count: 1 (confidence: 0.80)
2025-05-28 11:03:13,216 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 11:03:13,220 - src.groq_client - INFO - Extracted plant_types: ['coal'] (confidence: 0.80)
2025-05-28 11:03:14,281 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 11:03:14,282 - src.groq_client - INFO - Extracted ppa_flag: None (confidence: 0.10)
2025-05-28 11:03:16,493 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 11:03:16,495 - src.groq_client - INFO - Extracted currency_in: None (confidence: 0.10)
2025-05-28 11:03:17,400 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 11:03:17,403 - src.groq_client - INFO - Extracted financial_year: 04-03 (confidence: 0.90)
2025-05-28 11:03:17,905 - src.groq_client - INFO - LLM extraction completed
2025-05-28 11:03:17,906 - src.simple_pipeline - INFO - ✅ Organizational extraction completed
2025-05-28 11:03:17,907 - src.simple_pipeline - INFO - 🔧 Step 4: Extracting plant details with smart field-by-field approach
2025-05-28 11:03:17,907 - src.simple_pipeline - INFO - 🧠 Analyzing cached content for plant details extraction
2025-05-28 11:03:17,907 - src.plant_details_extractor - INFO - Starting plant details extraction for: Jhajjar Power Plant
2025-05-28 11:03:17,907 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field name: 'NoneType' object has no attribute 'client'
2025-05-28 11:03:17,907 - src.plant_details_extractor - INFO - Extracted name: None (confidence: 0.00)
2025-05-28 11:03:18,409 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field plant_type: 'NoneType' object has no attribute 'client'
2025-05-28 11:03:18,410 - src.plant_details_extractor - INFO - Extracted plant_type: None (confidence: 0.00)
2025-05-28 11:03:18,911 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field plant_address: 'NoneType' object has no attribute 'client'
2025-05-28 11:03:18,912 - src.plant_details_extractor - INFO - Extracted plant_address: None (confidence: 0.00)
2025-05-28 11:03:19,413 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field lat: 'NoneType' object has no attribute 'client'
2025-05-28 11:03:19,414 - src.plant_details_extractor - INFO - Extracted lat: None (confidence: 0.00)
2025-05-28 11:03:19,916 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field long: 'NoneType' object has no attribute 'client'
2025-05-28 11:03:19,916 - src.plant_details_extractor - INFO - Extracted long: None (confidence: 0.00)
2025-05-28 11:03:20,418 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field units_id: 'NoneType' object has no attribute 'client'
2025-05-28 11:03:20,418 - src.plant_details_extractor - INFO - Extracted units_id: None (confidence: 0.00)
2025-05-28 11:03:20,920 - src.plant_details_extractor - ERROR - Failed to extract grid_connectivity: No prompt template found for plant field: grid_connectivity
2025-05-28 11:03:20,921 - src.plant_details_extractor - ERROR - Failed to extract ppa_details: '\n  "description"'
2025-05-28 11:03:20,921 - src.plant_details_extractor - INFO - Using organization name for plant name: CLP India Private Limited
2025-05-28 11:03:20,921 - src.plant_details_extractor - INFO - Derived plant_type from org data: coal
2025-05-28 11:03:20,921 - src.plant_details_extractor - INFO - Plant details extraction completed for: Jhajjar Power Plant
2025-05-28 11:03:20,921 - src.simple_pipeline - INFO - ✅ Field 'name' extracted from cache
2025-05-28 11:03:20,921 - src.simple_pipeline - INFO - ✅ Field 'plant_type' extracted from cache
2025-05-28 11:03:20,921 - src.simple_pipeline - INFO - ✅ Field 'plant_id' extracted from cache
2025-05-28 11:03:20,922 - src.simple_pipeline - INFO - 🔍 Field 'lat' is missing - will search specifically
2025-05-28 11:03:20,922 - src.simple_pipeline - INFO - 🔍 Field 'long' is missing - will search specifically
2025-05-28 11:03:20,922 - src.simple_pipeline - INFO - 🔍 Field 'plant_address' is missing - will search specifically
2025-05-28 11:03:20,922 - src.simple_pipeline - INFO - 🔍 Field 'grid_connectivity_maps' is missing - will search specifically
2025-05-28 11:03:20,922 - src.simple_pipeline - INFO - 🔍 Field 'ppa_details' is missing - will search specifically
2025-05-28 11:03:20,922 - src.simple_pipeline - INFO - 🔍 Field 'units_id' is missing - will search specifically
2025-05-28 11:03:20,922 - src.simple_pipeline - INFO - 🎯 Starting targeted searches for 6 missing fields
2025-05-28 11:03:20,923 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'lat' data
2025-05-28 11:03:20,923 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant latitude coordinates GPS location'
2025-05-28 11:03:51,543 - src.serp_client - ERROR - Unexpected error in SERP API search: 
2025-05-28 11:04:26,538 - src.serp_client - ERROR - Unexpected error in SERP API search: 
2025-05-28 11:04:48,779 - src.simple_pipeline - INFO - Scraping 1/3: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 11:04:56,188 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-28 11:04:57,189 - src.simple_pipeline - INFO - Scraping 2/3: https://database.earth/countries/india/regions/haryana/cities/jhajjar
2025-05-28 11:04:58,763 - src.simple_pipeline - INFO - Successfully scraped https://database.earth/countries/india/regions/haryana/cities/jhajjar (685 chars)
2025-05-28 11:04:59,764 - src.simple_pipeline - INFO - Scraping 3/3: https://www.latlong.net/place/jhajjar-haryana-india-21959.html
2025-05-28 11:05:01,004 - src.simple_pipeline - INFO - Successfully scraped https://www.latlong.net/place/jhajjar-haryana-india-21959.html (1006 chars)
2025-05-28 11:05:02,007 - src.simple_pipeline - INFO - ✅ Found targeted content for 'lat' - 3 sources
2025-05-28 11:05:02,009 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'lat' using RAG: 28.607111
2025-05-28 11:05:02,009 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'long' data
2025-05-28 11:05:02,009 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant longitude coordinates GPS location'
2025-05-28 11:05:32,539 - src.serp_client - ERROR - Unexpected error in SERP API search: 
2025-05-28 11:05:44,522 - src.simple_pipeline - INFO - Scraping 1/3: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 11:05:56,136 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-28 11:05:57,137 - src.simple_pipeline - INFO - Scraping 2/3: https://database.earth/countries/india/regions/haryana/cities/jhajjar
2025-05-28 11:06:00,942 - src.simple_pipeline - INFO - Successfully scraped https://database.earth/countries/india/regions/haryana/cities/jhajjar (685 chars)
2025-05-28 11:06:01,944 - src.simple_pipeline - INFO - Scraping 3/3: https://www.latlong.net/place/jhajjar-haryana-india-21959.html
2025-05-28 11:06:03,267 - src.simple_pipeline - INFO - Successfully scraped https://www.latlong.net/place/jhajjar-haryana-india-21959.html (1006 chars)
2025-05-28 11:06:04,271 - src.simple_pipeline - INFO - ✅ Found targeted content for 'long' - 3 sources
2025-05-28 11:06:04,273 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'long' using RAG: 76.6565
2025-05-28 11:06:04,273 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'plant_address' data
2025-05-28 11:06:04,273 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant complete address location street city'
2025-05-28 11:06:17,007 - src.simple_pipeline - INFO - Scraping 1/2: https://www.bloomberg.com/profile/company/0808736D:IN
2025-05-28 11:06:21,746 - src.simple_pipeline - INFO - Successfully scraped https://www.bloomberg.com/profile/company/0808736D:IN (193 chars)
2025-05-28 11:06:22,747 - src.simple_pipeline - INFO - Scraping 2/2: https://www.dnb.com/business-directory/company-profiles.jhajjar_power_limited.f57a995e946f0a1cb2ff2978d567eb6d.html
2025-05-28 11:06:23,189 - src.scraper_client - WARNING - Failed to scrape https://www.dnb.com/business-directory/company-profiles.jhajjar_power_limited.f57a995e946f0a1cb2ff2978d567eb6d.html: HTTP 403
2025-05-28 11:06:23,190 - src.simple_pipeline - WARNING - Content too short or failed for https://www.dnb.com/business-directory/company-profiles.jhajjar_power_limited.f57a995e946f0a1cb2ff2978d567eb6d.html
2025-05-28 11:06:24,191 - src.simple_pipeline - INFO - ✅ Found targeted content for 'plant_address' - 1 sources
2025-05-28 11:06:24,193 - src.simple_pipeline - INFO - 📝 Content found for 'plant_address' but RAG extraction failed
2025-05-28 11:06:24,193 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'grid_connectivity_maps' data
2025-05-28 11:06:24,194 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant grid connection substation transmission line'
2025-05-28 11:06:29,403 - src.simple_pipeline - INFO - Scraping 1/3: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf
2025-05-28 11:06:41,859 - src.pdf_processor - INFO - Successfully extracted 132704 chars from PDF using pdfplumber: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf
2025-05-28 11:06:41,862 - src.scraper_client - INFO - Successfully processed PDF: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf (50003 chars, score: 1.00)
2025-05-28 11:06:41,862 - src.simple_pipeline - INFO - Successfully scraped https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf (50003 chars)
2025-05-28 11:06:42,864 - src.simple_pipeline - INFO - Scraping 2/3: http://www.gajendrahaldea.in/pdf/Jhajjar-Transmission-MSS.pdf
2025-05-28 11:07:08,833 - src.pdf_processor - INFO - Successfully extracted 112384 chars from PDF using pdfplumber: http://www.gajendrahaldea.in/pdf/Jhajjar-Transmission-MSS.pdf
2025-05-28 11:07:08,836 - src.scraper_client - INFO - Successfully processed PDF: http://www.gajendrahaldea.in/pdf/Jhajjar-Transmission-MSS.pdf (50003 chars, score: 1.00)
2025-05-28 11:07:08,837 - src.simple_pipeline - INFO - Successfully scraped http://www.gajendrahaldea.in/pdf/Jhajjar-Transmission-MSS.pdf (50003 chars)
2025-05-28 11:07:09,838 - src.simple_pipeline - INFO - Scraping 3/3: https://www.indigrid.co.in/portfolio-assets/jhajjar-kt-transco-pvt-ltd-jktpl/
2025-05-28 11:07:13,398 - src.simple_pipeline - INFO - Successfully scraped https://www.indigrid.co.in/portfolio-assets/jhajjar-kt-transco-pvt-ltd-jktpl/ (846 chars)
2025-05-28 11:07:14,400 - src.simple_pipeline - INFO - ✅ Found targeted content for 'grid_connectivity_maps' - 3 sources
2025-05-28 11:07:14,402 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'grid_connectivity_maps' using RAG: [{'description': 'Grid connectivity details for Jhajjar Power Plant', 'details': [{'substation_name': 'Sonipat Substation', 'substation_type': 'transmission', 'capacity': '400 kV', 'latitude': '', 'longitude': '', 'description': 'Primary transmission connection point - approximately 70 km northeast', 'projects': [{'description': '400 kV transmission line from Jhajjar to Sonipat', 'distance': '70 km'}]}, {'substation_name': 'Mahendragarh Substation', 'substation_type': 'transmission', 'capacity': '400 kV', 'latitude': '', 'longitude': '', 'description': 'Secondary transmission connection point - approximately 50 km southwest', 'projects': [{'description': '400 kV transmission line from Jhajjar to Mahendragarh', 'distance': '50 km'}]}]}]
2025-05-28 11:07:14,402 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'ppa_details' data
2025-05-28 11:07:14,402 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant power purchase agreement PPA contract details'
2025-05-28 11:07:19,971 - src.simple_pipeline - INFO - Scraping 1/3: https://www.derc.gov.in/sites/default/files/NDPL%20-%20Jhajjar%20Power%20Ltd..pdf
2025-05-28 11:07:34,624 - src.pdf_processor - INFO - Successfully extracted 36539 chars from PDF using pdfplumber: https://www.derc.gov.in/sites/default/files/NDPL%20-%20Jhajjar%20Power%20Ltd..pdf
2025-05-28 11:07:34,629 - src.scraper_client - INFO - Successfully processed PDF: https://www.derc.gov.in/sites/default/files/NDPL%20-%20Jhajjar%20Power%20Ltd..pdf (36524 chars, score: 1.00)
2025-05-28 11:07:34,630 - src.simple_pipeline - INFO - Successfully scraped https://www.derc.gov.in/sites/default/files/NDPL%20-%20Jhajjar%20Power%20Ltd..pdf (36524 chars)
2025-05-28 11:07:35,631 - src.simple_pipeline - INFO - Scraping 2/3: https://cercind.gov.in/2022/orders/363-MP-2019.pdf
2025-05-28 11:07:41,842 - src.pdf_processor - INFO - Successfully extracted 17990 chars from PDF using pdfplumber: https://cercind.gov.in/2022/orders/363-MP-2019.pdf
2025-05-28 11:07:41,843 - src.scraper_client - INFO - Successfully processed PDF: https://cercind.gov.in/2022/orders/363-MP-2019.pdf (17981 chars, score: 1.00)
2025-05-28 11:07:41,844 - src.simple_pipeline - INFO - Successfully scraped https://cercind.gov.in/2022/orders/363-MP-2019.pdf (17981 chars)
2025-05-28 11:07:42,845 - src.simple_pipeline - INFO - Scraping 3/3: https://cercind.gov.in/2023/orders/637-MP-2020.pdf
2025-05-28 11:07:52,588 - src.pdf_processor - INFO - Successfully extracted 111606 chars from PDF using pdfplumber: https://cercind.gov.in/2023/orders/637-MP-2020.pdf
2025-05-28 11:07:52,590 - src.scraper_client - INFO - Successfully processed PDF: https://cercind.gov.in/2023/orders/637-MP-2020.pdf (50003 chars, score: 1.00)
2025-05-28 11:07:52,590 - src.simple_pipeline - INFO - Successfully scraped https://cercind.gov.in/2023/orders/637-MP-2020.pdf (50003 chars)
2025-05-28 11:07:53,592 - src.simple_pipeline - INFO - ✅ Found targeted content for 'ppa_details' - 3 sources
2025-05-28 11:07:53,595 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'ppa_details' using RAG: [{'description': 'Power Purchase Agreement for Jhajjar Power Plant', 'capacity': '1320 MW', 'capacity_unit': 'MW', 'start_date': '2012', 'end_date': '', 'tenure': 30, 'tenure_type': 'Years', 'respondents': [{'name': 'Haryana State Distribution Companies', 'capacity': '1188 MW', 'currency': 'INR', 'price': '', 'price_unit': 'INR/kWh'}, {'name': 'External Power Buyers', 'capacity': '132 MW', 'currency': 'INR', 'price': '', 'price_unit': 'INR/kWh'}]}]
2025-05-28 11:07:53,595 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'units_id' data
2025-05-28 11:07:53,595 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant units generators unit numbers'
2025-05-28 11:08:18,328 - src.simple_pipeline - INFO - Scraping 1/3: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf
2025-05-28 11:08:24,126 - src.pdf_processor - INFO - Successfully extracted 132704 chars from PDF using pdfplumber: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf
2025-05-28 11:08:24,128 - src.scraper_client - INFO - Successfully processed PDF: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf (50003 chars, score: 1.00)
2025-05-28 11:08:24,128 - src.simple_pipeline - INFO - Successfully scraped https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf (50003 chars)
2025-05-28 11:08:25,130 - src.simple_pipeline - INFO - Scraping 2/3: https://www.apraava.com/getmedia/8932fe7b-f82d-4a82-ab8c-32843bd9de2e/2020-21.pdf
2025-05-28 11:08:36,902 - src.pdf_processor - INFO - Successfully extracted 89455 chars from PDF using pdfplumber: https://www.apraava.com/getmedia/8932fe7b-f82d-4a82-ab8c-32843bd9de2e/2020-21.pdf
2025-05-28 11:08:36,904 - src.scraper_client - INFO - Successfully processed PDF: https://www.apraava.com/getmedia/8932fe7b-f82d-4a82-ab8c-32843bd9de2e/2020-21.pdf (50003 chars, score: 1.00)
2025-05-28 11:08:36,904 - src.simple_pipeline - INFO - Successfully scraped https://www.apraava.com/getmedia/8932fe7b-f82d-4a82-ab8c-32843bd9de2e/2020-21.pdf (50003 chars)
2025-05-28 11:08:37,906 - src.simple_pipeline - INFO - Scraping 3/3: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 11:08:38,671 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-28 11:08:39,673 - src.simple_pipeline - INFO - ✅ Found targeted content for 'units_id' - 3 sources
2025-05-28 11:08:39,683 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'units_id' using RAG: ['Unit 1', 'Unit 3', 'Unit 250']
2025-05-28 11:08:39,689 - src.simple_pipeline - INFO - 💾 Targeted content saved to jhajjar_power_plant_targeted_content_20250528_110839.json
2025-05-28 11:08:39,691 - src.simple_pipeline - INFO - 🎉 Simplified extraction completed for: Jhajjar Power Plant
2025-05-28 11:08:39,691 - src.simple_pipeline - INFO - ⏱️  Total time: 366.7s
2025-05-28 11:08:39,691 - src.simple_pipeline - INFO - 💾 Cache efficiency: 3 fields from cache, 6 targeted searches
2025-05-28 11:08:39,693 - src.simple_pipeline - INFO - 📊 Organizational results saved to jhajjar_org_groq_rag_20250528_110839.json
2025-05-28 11:08:39,694 - src.simple_pipeline - INFO - 🔧 Plant details results saved to jhajjar_plant_groq_rag_20250528_110839.json
2025-05-28 11:08:39,694 - src.simple_pipeline - INFO - 📈 Extraction info saved to jhajjar_extraction_info_groq_rag_20250528_110839.json
2025-05-28 11:26:13,626 - src.enhanced_extractor - INFO - Enhanced data extractor initialized with Groq
2025-05-28 11:26:13,637 - src.plant_details_extractor - INFO - Plant details extractor initialized with Groq
2025-05-28 11:26:13,637 - src.simple_pipeline - INFO - 🔍 Starting simplified extraction for: Jhajjar Power Plant
2025-05-28 11:26:13,637 - src.simple_pipeline - INFO - 📡 Step 1: Searching for 'Jhajjar Power Plant' - getting top 5 links
2025-05-28 11:26:44,488 - src.serp_client - ERROR - Unexpected error in SERP API search: 
2025-05-28 11:27:10,801 - src.simple_pipeline - INFO - 🌐 Step 2: Scraping 5 top results
2025-05-28 11:27:10,802 - src.simple_pipeline - INFO - Scraping 1/5: https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html
2025-05-28 11:27:17,658 - src.simple_pipeline - INFO - Successfully scraped https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html (516 chars)
2025-05-28 11:27:18,659 - src.simple_pipeline - INFO - Scraping 2/5: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 11:27:19,257 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-28 11:27:20,259 - src.simple_pipeline - INFO - Scraping 3/5: https://apraava.com/projects/jhajjar-power-plant
2025-05-28 11:27:22,204 - src.simple_pipeline - INFO - Successfully scraped https://apraava.com/projects/jhajjar-power-plant (791 chars)
2025-05-28 11:27:23,206 - src.simple_pipeline - INFO - Scraping 4/5: https://m.economictimes.com/industry/energy/power/jindal-power-emerges-frontrunner-to-acquire-apraavas-haryana-project/articleshow/*********.cms
2025-05-28 11:27:28,898 - src.simple_pipeline - INFO - Successfully scraped https://m.economictimes.com/industry/energy/power/jindal-power-emerges-frontrunner-to-acquire-apraavas-haryana-project/articleshow/*********.cms (2246 chars)
2025-05-28 11:27:29,900 - src.simple_pipeline - INFO - Scraping 5/5: https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr
2025-05-28 11:27:39,896 - src.simple_pipeline - INFO - Successfully scraped https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr (3864 chars)
2025-05-28 11:27:40,898 - src.simple_pipeline - INFO - ✅ Initial scraping completed: 5 pages scraped
2025-05-28 11:27:40,898 - src.simple_pipeline - INFO - 📊 Step 3: Extracting organizational details from scraped content
2025-05-28 11:27:40,899 - src.enhanced_extractor - INFO - Content analysis: {'total_sources': 5, 'total_content': 7907, 'avg_relevance': 0.8380000000000001, 'high_quality_sources': 1, 'source_type_diversity': 3, 'source_types': ['company_official', 'wikipedia', 'other']}
2025-05-28 11:27:40,899 - src.enhanced_extractor - INFO - Using basic extraction with validation
2025-05-28 11:27:40,899 - src.groq_client - INFO - Starting LLM extraction for 5 content pieces
2025-05-28 11:27:41,297 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 11:27:41,569 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 11:27:41,571 - src.groq_client - INFO - Extracted cfpp_type: joint_venture (confidence: 0.90)
2025-05-28 11:27:42,367 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 11:27:42,370 - src.groq_client - INFO - Extracted country_name: India (confidence: 0.80)
2025-05-28 11:27:43,095 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 11:27:43,102 - src.groq_client - INFO - Extracted province: Haryana (confidence: 0.70)
2025-05-28 11:27:43,822 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 11:27:43,825 - src.groq_client - INFO - Extracted plants_count: 1 (confidence: 0.80)
2025-05-28 11:27:44,548 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 11:27:44,552 - src.groq_client - INFO - Extracted plant_types: ['coal'] (confidence: 0.80)
2025-05-28 11:27:45,610 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 11:27:45,614 - src.groq_client - INFO - Extracted ppa_flag: Plant (confidence: 0.80)
2025-05-28 11:27:46,355 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 11:27:46,358 - src.groq_client - INFO - Extracted currency_in: INR (confidence: 0.90)
2025-05-28 11:27:47,094 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 11:27:47,098 - src.groq_client - INFO - Extracted financial_year: 04-03 (confidence: 0.90)
2025-05-28 11:27:47,599 - src.groq_client - INFO - LLM extraction completed
2025-05-28 11:27:47,601 - src.simple_pipeline - INFO - ✅ Organizational extraction completed
2025-05-28 11:27:47,601 - src.simple_pipeline - INFO - 🔧 Step 4: Extracting plant details with smart field-by-field approach
2025-05-28 11:27:47,601 - src.simple_pipeline - INFO - 🧠 Analyzing cached content for plant details extraction
2025-05-28 11:27:47,601 - src.plant_details_extractor - INFO - Starting plant details extraction for: Jhajjar Power Plant
2025-05-28 11:27:47,601 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field name: 'NoneType' object has no attribute 'client'
2025-05-28 11:27:47,601 - src.plant_details_extractor - INFO - Extracted name: None (confidence: 0.00)
2025-05-28 11:27:48,103 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field plant_type: 'NoneType' object has no attribute 'client'
2025-05-28 11:27:48,103 - src.plant_details_extractor - INFO - Extracted plant_type: None (confidence: 0.00)
2025-05-28 11:27:48,605 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field plant_address: 'NoneType' object has no attribute 'client'
2025-05-28 11:27:48,605 - src.plant_details_extractor - INFO - Extracted plant_address: None (confidence: 0.00)
2025-05-28 11:27:49,107 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field lat: 'NoneType' object has no attribute 'client'
2025-05-28 11:27:49,108 - src.plant_details_extractor - INFO - Extracted lat: None (confidence: 0.00)
2025-05-28 11:27:49,609 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field long: 'NoneType' object has no attribute 'client'
2025-05-28 11:27:49,610 - src.plant_details_extractor - INFO - Extracted long: None (confidence: 0.00)
2025-05-28 11:27:50,112 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field units_id: 'NoneType' object has no attribute 'client'
2025-05-28 11:27:50,112 - src.plant_details_extractor - INFO - Extracted units_id: None (confidence: 0.00)
2025-05-28 11:27:50,614 - src.plant_details_extractor - ERROR - Failed to extract grid_connectivity: No prompt template found for plant field: grid_connectivity
2025-05-28 11:27:50,614 - src.plant_details_extractor - ERROR - Failed to extract ppa_details: '\n  "description"'
2025-05-28 11:27:50,615 - src.plant_details_extractor - INFO - Using organization name for plant name: CLP India Private Limited
2025-05-28 11:27:50,615 - src.plant_details_extractor - INFO - Derived plant_type from org data: coal
2025-05-28 11:27:50,615 - src.plant_details_extractor - INFO - Plant details extraction completed for: Jhajjar Power Plant
2025-05-28 11:27:50,615 - src.simple_pipeline - INFO - ✅ Field 'name' extracted from cache
2025-05-28 11:27:50,615 - src.simple_pipeline - INFO - ✅ Field 'plant_type' extracted from cache
2025-05-28 11:27:50,615 - src.simple_pipeline - INFO - ✅ Field 'plant_id' extracted from cache
2025-05-28 11:27:50,615 - src.simple_pipeline - INFO - 🔍 Field 'lat' is missing - will search specifically
2025-05-28 11:27:50,616 - src.simple_pipeline - INFO - 🔍 Field 'long' is missing - will search specifically
2025-05-28 11:27:50,616 - src.simple_pipeline - INFO - 🔍 Field 'plant_address' is missing - will search specifically
2025-05-28 11:27:50,616 - src.simple_pipeline - INFO - 🔍 Field 'grid_connectivity_maps' is missing - will search specifically
2025-05-28 11:27:50,616 - src.simple_pipeline - INFO - 🔍 Field 'ppa_details' is missing - will search specifically
2025-05-28 11:27:50,616 - src.simple_pipeline - INFO - 🔍 Field 'units_id' is missing - will search specifically
2025-05-28 11:27:50,616 - src.simple_pipeline - INFO - 🎯 Starting targeted searches for 6 missing fields
2025-05-28 11:27:50,616 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'lat' data
2025-05-28 11:27:50,616 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant latitude coordinates GPS location'
2025-05-28 11:27:55,586 - src.simple_pipeline - INFO - Scraping 1/3: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 11:27:57,196 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-28 11:27:58,197 - src.simple_pipeline - INFO - Scraping 2/3: https://database.earth/countries/india/regions/haryana/cities/jhajjar
2025-05-28 11:27:59,032 - src.simple_pipeline - INFO - Successfully scraped https://database.earth/countries/india/regions/haryana/cities/jhajjar (685 chars)
2025-05-28 11:28:00,034 - src.simple_pipeline - INFO - Scraping 3/3: https://www.latlong.net/place/jhajjar-haryana-india-21959.html
2025-05-28 11:28:01,273 - src.simple_pipeline - INFO - Successfully scraped https://www.latlong.net/place/jhajjar-haryana-india-21959.html (1006 chars)
2025-05-28 11:28:02,275 - src.simple_pipeline - INFO - ✅ Found targeted content for 'lat' - 3 sources
2025-05-28 11:28:02,277 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'lat' using RAG: 28.607111
2025-05-28 11:28:02,278 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'long' data
2025-05-28 11:28:02,278 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant longitude coordinates GPS location'
2025-05-28 11:28:32,574 - src.serp_client - ERROR - Unexpected error in SERP API search: 
2025-05-28 11:28:52,832 - src.simple_pipeline - INFO - Scraping 1/3: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 11:28:54,395 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-28 11:28:55,397 - src.simple_pipeline - INFO - Scraping 2/3: https://database.earth/countries/india/regions/haryana/cities/jhajjar
2025-05-28 11:28:56,032 - src.simple_pipeline - INFO - Successfully scraped https://database.earth/countries/india/regions/haryana/cities/jhajjar (685 chars)
2025-05-28 11:28:57,034 - src.simple_pipeline - INFO - Scraping 3/3: https://www.latlong.net/place/jhajjar-haryana-india-21959.html
2025-05-28 11:28:57,898 - src.simple_pipeline - INFO - Successfully scraped https://www.latlong.net/place/jhajjar-haryana-india-21959.html (1006 chars)
2025-05-28 11:28:58,900 - src.simple_pipeline - INFO - ✅ Found targeted content for 'long' - 3 sources
2025-05-28 11:28:58,900 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'long' using RAG: 76.6565
2025-05-28 11:28:58,900 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'plant_address' data
2025-05-28 11:28:58,900 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant complete address location street city'
2025-05-28 11:29:27,195 - src.simple_pipeline - INFO - Scraping 1/2: https://www.bloomberg.com/profile/company/0808736D:IN
2025-05-28 11:29:29,698 - src.simple_pipeline - INFO - Successfully scraped https://www.bloomberg.com/profile/company/0808736D:IN (193 chars)
2025-05-28 11:29:30,700 - src.simple_pipeline - INFO - Scraping 2/2: https://www.dnb.com/business-directory/company-profiles.jhajjar_power_limited.f57a995e946f0a1cb2ff2978d567eb6d.html
2025-05-28 11:29:30,982 - src.scraper_client - WARNING - Failed to scrape https://www.dnb.com/business-directory/company-profiles.jhajjar_power_limited.f57a995e946f0a1cb2ff2978d567eb6d.html: HTTP 403
2025-05-28 11:29:30,982 - src.simple_pipeline - WARNING - Content too short or failed for https://www.dnb.com/business-directory/company-profiles.jhajjar_power_limited.f57a995e946f0a1cb2ff2978d567eb6d.html
2025-05-28 11:29:31,985 - src.simple_pipeline - INFO - ✅ Found targeted content for 'plant_address' - 1 sources
2025-05-28 11:29:31,986 - src.simple_pipeline - INFO - 📝 Content found for 'plant_address' but RAG extraction failed
2025-05-28 11:29:31,987 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'grid_connectivity_maps' data
2025-05-28 11:29:31,987 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant grid connection substation transmission line'
2025-05-28 11:29:47,363 - src.simple_pipeline - INFO - Scraping 1/3: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf
2025-05-28 11:29:52,672 - src.pdf_processor - INFO - Successfully extracted 132704 chars from PDF using pdfplumber: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf
2025-05-28 11:29:52,674 - src.scraper_client - INFO - Successfully processed PDF: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf (50003 chars, score: 1.00)
2025-05-28 11:29:52,675 - src.simple_pipeline - INFO - Successfully scraped https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf (50003 chars)
2025-05-28 11:29:53,676 - src.simple_pipeline - INFO - Scraping 2/3: http://www.gajendrahaldea.in/pdf/Jhajjar-Transmission-MSS.pdf
2025-05-28 11:30:08,727 - src.pdf_processor - INFO - Successfully extracted 112384 chars from PDF using pdfplumber: http://www.gajendrahaldea.in/pdf/Jhajjar-Transmission-MSS.pdf
2025-05-28 11:30:08,729 - src.scraper_client - INFO - Successfully processed PDF: http://www.gajendrahaldea.in/pdf/Jhajjar-Transmission-MSS.pdf (50003 chars, score: 1.00)
2025-05-28 11:30:08,730 - src.simple_pipeline - INFO - Successfully scraped http://www.gajendrahaldea.in/pdf/Jhajjar-Transmission-MSS.pdf (50003 chars)
2025-05-28 11:30:09,731 - src.simple_pipeline - INFO - Scraping 3/3: https://www.indigrid.co.in/portfolio-assets/jhajjar-kt-transco-pvt-ltd-jktpl/
2025-05-28 11:30:12,695 - src.simple_pipeline - INFO - Successfully scraped https://www.indigrid.co.in/portfolio-assets/jhajjar-kt-transco-pvt-ltd-jktpl/ (846 chars)
2025-05-28 11:30:13,698 - src.simple_pipeline - INFO - ✅ Found targeted content for 'grid_connectivity_maps' - 3 sources
2025-05-28 11:30:13,700 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'grid_connectivity_maps' using RAG: [{'description': 'Grid connectivity details for Jhajjar Power Plant', 'details': [{'substation_name': 'Sonipat Substation', 'substation_type': 'transmission', 'capacity': '400 kV', 'latitude': '', 'longitude': '', 'description': 'Primary transmission connection point - approximately 70 km northeast', 'projects': [{'description': '400 kV transmission line from Jhajjar to Sonipat', 'distance': '70 km'}]}, {'substation_name': 'Mahendragarh Substation', 'substation_type': 'transmission', 'capacity': '400 kV', 'latitude': '', 'longitude': '', 'description': 'Secondary transmission connection point - approximately 50 km southwest', 'projects': [{'description': '400 kV transmission line from Jhajjar to Mahendragarh', 'distance': '50 km'}]}]}]
2025-05-28 11:30:13,700 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'ppa_details' data
2025-05-28 11:30:13,700 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant power purchase agreement PPA contract details'
2025-05-28 11:30:16,607 - src.simple_pipeline - INFO - Scraping 1/3: https://www.derc.gov.in/sites/default/files/NDPL%20-%20Jhajjar%20Power%20Ltd..pdf
2025-05-28 11:30:32,610 - src.pdf_processor - INFO - Successfully extracted 36539 chars from PDF using pdfplumber: https://www.derc.gov.in/sites/default/files/NDPL%20-%20Jhajjar%20Power%20Ltd..pdf
2025-05-28 11:30:32,612 - src.scraper_client - INFO - Successfully processed PDF: https://www.derc.gov.in/sites/default/files/NDPL%20-%20Jhajjar%20Power%20Ltd..pdf (36524 chars, score: 1.00)
2025-05-28 11:30:32,612 - src.simple_pipeline - INFO - Successfully scraped https://www.derc.gov.in/sites/default/files/NDPL%20-%20Jhajjar%20Power%20Ltd..pdf (36524 chars)
2025-05-28 11:30:33,614 - src.simple_pipeline - INFO - Scraping 2/3: https://cercind.gov.in/2022/orders/363-MP-2019.pdf
2025-05-28 11:30:37,191 - src.pdf_processor - INFO - Successfully extracted 17990 chars from PDF using pdfplumber: https://cercind.gov.in/2022/orders/363-MP-2019.pdf
2025-05-28 11:30:37,192 - src.scraper_client - INFO - Successfully processed PDF: https://cercind.gov.in/2022/orders/363-MP-2019.pdf (17981 chars, score: 1.00)
2025-05-28 11:30:37,192 - src.simple_pipeline - INFO - Successfully scraped https://cercind.gov.in/2022/orders/363-MP-2019.pdf (17981 chars)
2025-05-28 11:30:38,194 - src.simple_pipeline - INFO - Scraping 3/3: https://cercind.gov.in/2023/orders/637-MP-2020.pdf
2025-05-28 11:30:44,227 - src.pdf_processor - INFO - Successfully extracted 111606 chars from PDF using pdfplumber: https://cercind.gov.in/2023/orders/637-MP-2020.pdf
2025-05-28 11:30:44,229 - src.scraper_client - INFO - Successfully processed PDF: https://cercind.gov.in/2023/orders/637-MP-2020.pdf (50003 chars, score: 1.00)
2025-05-28 11:30:44,229 - src.simple_pipeline - INFO - Successfully scraped https://cercind.gov.in/2023/orders/637-MP-2020.pdf (50003 chars)
2025-05-28 11:30:45,231 - src.simple_pipeline - INFO - ✅ Found targeted content for 'ppa_details' - 3 sources
2025-05-28 11:30:45,234 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'ppa_details' using RAG: [{'description': 'Power Purchase Agreement for Jhajjar Power Plant', 'capacity': '1320 MW', 'capacity_unit': 'MW', 'start_date': '2012', 'end_date': '', 'tenure': 30, 'tenure_type': 'Years', 'respondents': [{'name': 'Haryana State Distribution Companies', 'capacity': '1188 MW', 'currency': 'INR', 'price': '', 'price_unit': 'INR/kWh'}, {'name': 'External Power Buyers', 'capacity': '132 MW', 'currency': 'INR', 'price': '', 'price_unit': 'INR/kWh'}]}]
2025-05-28 11:30:45,234 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'units_id' data
2025-05-28 11:30:45,234 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant units generators unit numbers'
2025-05-28 11:31:15,572 - src.serp_client - ERROR - Unexpected error in SERP API search: 
2025-05-28 11:31:28,208 - src.simple_pipeline - INFO - Scraping 1/3: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf
2025-05-28 11:31:37,470 - src.pdf_processor - INFO - Successfully extracted 132704 chars from PDF using pdfplumber: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf
2025-05-28 11:31:37,473 - src.scraper_client - INFO - Successfully processed PDF: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf (50003 chars, score: 1.00)
2025-05-28 11:31:37,473 - src.simple_pipeline - INFO - Successfully scraped https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf (50003 chars)
2025-05-28 11:31:38,474 - src.simple_pipeline - INFO - Scraping 2/3: https://www.cercind.gov.in/2020/orders/114-MP-2018.pdf
2025-05-28 11:31:47,058 - src.pdf_processor - INFO - Successfully extracted 53646 chars from PDF using pdfplumber: https://www.cercind.gov.in/2020/orders/114-MP-2018.pdf
2025-05-28 11:31:47,060 - src.scraper_client - INFO - Successfully processed PDF: https://www.cercind.gov.in/2020/orders/114-MP-2018.pdf (50003 chars, score: 1.00)
2025-05-28 11:31:47,060 - src.simple_pipeline - INFO - Successfully scraped https://www.cercind.gov.in/2020/orders/114-MP-2018.pdf (50003 chars)
2025-05-28 11:31:48,062 - src.simple_pipeline - INFO - Scraping 3/3: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 11:31:49,570 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-28 11:31:50,572 - src.simple_pipeline - INFO - ✅ Found targeted content for 'units_id' - 3 sources
2025-05-28 11:31:50,582 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'units_id' using RAG: ['Unit 1', 'Unit 2', 'Unit 3', 'Unit 250']
2025-05-28 11:31:50,587 - src.simple_pipeline - INFO - 💾 Targeted content saved to jhajjar_power_plant_targeted_content_20250528_113150.json
2025-05-28 11:31:50,587 - src.simple_pipeline - INFO - 🎉 Simplified extraction completed for: Jhajjar Power Plant
2025-05-28 11:31:50,587 - src.simple_pipeline - INFO - ⏱️  Total time: 337.0s
2025-05-28 11:31:50,588 - src.simple_pipeline - INFO - 💾 Cache efficiency: 3 fields from cache, 6 targeted searches
2025-05-28 11:31:50,589 - src.simple_pipeline - INFO - 📊 Organizational results saved to jhajjar_org_groq_rag_20250528_113150.json
2025-05-28 11:31:50,589 - src.simple_pipeline - INFO - 🔧 Plant details results saved to jhajjar_plant_groq_rag_20250528_113150.json
2025-05-28 11:31:50,590 - src.simple_pipeline - INFO - 📈 Extraction info saved to jhajjar_extraction_info_groq_rag_20250528_113150.json
2025-05-28 11:48:11,370 - src.enhanced_extractor - INFO - Enhanced data extractor initialized with Groq
2025-05-28 11:48:11,382 - src.plant_details_extractor - INFO - Plant details extractor initialized with Groq
2025-05-28 11:48:11,382 - src.simple_pipeline - INFO - 🔍 Starting simplified extraction for: Jhajjar Power Plant
2025-05-28 11:48:11,382 - src.simple_pipeline - INFO - 📡 Step 1: Searching for 'Jhajjar Power Plant' - getting top 5 links
2025-05-28 11:48:17,536 - src.simple_pipeline - INFO - 🌐 Step 2: Scraping 5 top results
2025-05-28 11:48:17,536 - src.simple_pipeline - INFO - Scraping 1/5: https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html
2025-05-28 11:48:18,638 - src.simple_pipeline - INFO - Successfully scraped https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html (516 chars)
2025-05-28 11:48:19,640 - src.simple_pipeline - INFO - Scraping 2/5: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 11:48:20,334 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-28 11:48:21,336 - src.simple_pipeline - INFO - Scraping 3/5: https://apraava.com/projects/jhajjar-power-plant
2025-05-28 11:48:23,241 - src.simple_pipeline - INFO - Successfully scraped https://apraava.com/projects/jhajjar-power-plant (791 chars)
2025-05-28 11:48:24,242 - src.simple_pipeline - INFO - Scraping 4/5: https://m.economictimes.com/industry/energy/power/jindal-power-emerges-frontrunner-to-acquire-apraavas-haryana-project/articleshow/*********.cms
2025-05-28 11:48:25,455 - src.simple_pipeline - INFO - Successfully scraped https://m.economictimes.com/industry/energy/power/jindal-power-emerges-frontrunner-to-acquire-apraavas-haryana-project/articleshow/*********.cms (2246 chars)
2025-05-28 11:48:26,456 - src.simple_pipeline - INFO - Scraping 5/5: https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr
2025-05-28 11:48:27,992 - src.simple_pipeline - INFO - Successfully scraped https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr (3864 chars)
2025-05-28 11:48:28,995 - src.simple_pipeline - INFO - ✅ Initial scraping completed: 5 pages scraped
2025-05-28 11:48:28,995 - src.simple_pipeline - INFO - 📊 Step 3: Extracting organizational details from scraped content
2025-05-28 11:48:28,996 - src.enhanced_extractor - INFO - Content analysis: {'total_sources': 5, 'total_content': 7907, 'avg_relevance': 0.8380000000000001, 'high_quality_sources': 1, 'source_type_diversity': 3, 'source_types': ['wikipedia', 'company_official', 'other']}
2025-05-28 11:48:28,996 - src.enhanced_extractor - INFO - Using basic extraction with validation
2025-05-28 11:48:28,996 - src.groq_client - INFO - Starting LLM extraction for 5 content pieces
2025-05-28 11:48:29,364 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 11:48:29,587 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 11:48:29,589 - src.groq_client - INFO - Extracted cfpp_type: joint_venture (confidence: 0.90)
2025-05-28 11:48:30,295 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 11:48:30,296 - src.groq_client - INFO - Extracted country_name: India (confidence: 0.80)
2025-05-28 11:48:31,010 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 11:48:31,011 - src.groq_client - INFO - Extracted province: Haryana (confidence: 0.70)
2025-05-28 11:48:31,730 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 11:48:31,732 - src.groq_client - INFO - Extracted plants_count: 1 (confidence: 0.80)
2025-05-28 11:48:32,926 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 11:48:32,929 - src.groq_client - INFO - Extracted plant_types: ['coal', 'gas', 'coal'] (confidence: 0.80)
2025-05-28 11:48:34,006 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 11:48:34,011 - src.groq_client - INFO - Extracted ppa_flag: Plant (confidence: 0.80)
2025-05-28 11:48:34,745 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 11:48:34,747 - src.groq_client - INFO - Extracted currency_in: INR (confidence: 0.90)
2025-05-28 11:48:35,569 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 11:48:35,572 - src.groq_client - INFO - Extracted financial_year: 04-03 (confidence: 0.90)
2025-05-28 11:48:36,073 - src.groq_client - INFO - LLM extraction completed
2025-05-28 11:48:36,075 - src.simple_pipeline - INFO - ✅ Organizational extraction completed
2025-05-28 11:48:36,075 - src.simple_pipeline - INFO - 🔧 Step 4: Extracting plant details with smart field-by-field approach
2025-05-28 11:48:36,076 - src.simple_pipeline - INFO - 🧠 Analyzing cached content for plant details extraction
2025-05-28 11:48:36,076 - src.plant_details_extractor - INFO - Starting plant details extraction for: Jhajjar Power Plant
2025-05-28 11:48:36,076 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field name: 'NoneType' object has no attribute 'client'
2025-05-28 11:48:36,076 - src.plant_details_extractor - INFO - Extracted name: None (confidence: 0.00)
2025-05-28 11:48:36,578 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field plant_type: 'NoneType' object has no attribute 'client'
2025-05-28 11:48:36,578 - src.plant_details_extractor - INFO - Extracted plant_type: None (confidence: 0.00)
2025-05-28 11:48:37,079 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field plant_address: 'NoneType' object has no attribute 'client'
2025-05-28 11:48:37,080 - src.plant_details_extractor - INFO - Extracted plant_address: None (confidence: 0.00)
2025-05-28 11:48:37,581 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field lat: 'NoneType' object has no attribute 'client'
2025-05-28 11:48:37,582 - src.plant_details_extractor - INFO - Extracted lat: None (confidence: 0.00)
2025-05-28 11:48:38,083 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field long: 'NoneType' object has no attribute 'client'
2025-05-28 11:48:38,084 - src.plant_details_extractor - INFO - Extracted long: None (confidence: 0.00)
2025-05-28 11:48:38,585 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field units_id: 'NoneType' object has no attribute 'client'
2025-05-28 11:48:38,586 - src.plant_details_extractor - INFO - Extracted units_id: None (confidence: 0.00)
2025-05-28 11:48:39,087 - src.plant_details_extractor - ERROR - Failed to extract grid_connectivity: No prompt template found for plant field: grid_connectivity
2025-05-28 11:48:39,087 - src.plant_details_extractor - ERROR - Failed to extract ppa_details: '\n  "capacity"'
2025-05-28 11:48:39,088 - src.plant_details_extractor - INFO - Using organization name for plant name: CLP India Private Limited
2025-05-28 11:48:39,088 - src.plant_details_extractor - INFO - Derived plant_type from org data: coal
2025-05-28 11:48:39,088 - src.plant_details_extractor - INFO - Plant details extraction completed for: Jhajjar Power Plant
2025-05-28 11:48:39,088 - src.simple_pipeline - INFO - ✅ Field 'name' extracted from cache
2025-05-28 11:48:39,088 - src.simple_pipeline - INFO - ✅ Field 'plant_type' extracted from cache
2025-05-28 11:48:39,088 - src.simple_pipeline - INFO - ✅ Field 'plant_id' extracted from cache
2025-05-28 11:48:39,088 - src.simple_pipeline - INFO - 🔍 Field 'lat' is missing - will search specifically
2025-05-28 11:48:39,088 - src.simple_pipeline - INFO - 🔍 Field 'long' is missing - will search specifically
2025-05-28 11:48:39,088 - src.simple_pipeline - INFO - 🔍 Field 'plant_address' is missing - will search specifically
2025-05-28 11:48:39,088 - src.simple_pipeline - INFO - 🔍 Field 'grid_connectivity_maps' is missing - will search specifically
2025-05-28 11:48:39,088 - src.simple_pipeline - INFO - 🔍 Field 'ppa_details' is missing - will search specifically
2025-05-28 11:48:39,089 - src.simple_pipeline - INFO - 🔍 Field 'units_id' is missing - will search specifically
2025-05-28 11:48:39,089 - src.simple_pipeline - INFO - 🎯 Starting targeted searches for 6 missing fields
2025-05-28 11:48:39,089 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'lat' data
2025-05-28 11:48:39,089 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant plant's own latitude coordinate decimal degrees GPS location'
2025-05-28 11:48:54,754 - src.simple_pipeline - INFO - Scraping 1/3: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 11:48:56,340 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-28 11:48:57,341 - src.simple_pipeline - INFO - Scraping 2/3: https://database.earth/countries/india/regions/haryana/cities/jhajjar
2025-05-28 11:48:59,024 - src.simple_pipeline - INFO - Successfully scraped https://database.earth/countries/india/regions/haryana/cities/jhajjar (685 chars)
2025-05-28 11:49:00,026 - src.simple_pipeline - INFO - Scraping 3/3: https://www.latlong.net/place/jhajjar-haryana-india-21959.html
2025-05-28 11:49:00,951 - src.simple_pipeline - INFO - Successfully scraped https://www.latlong.net/place/jhajjar-haryana-india-21959.html (1006 chars)
2025-05-28 11:49:01,953 - src.simple_pipeline - INFO - ✅ Found targeted content for 'lat' - 3 sources
2025-05-28 11:49:01,955 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'lat' using RAG: 28.607111
2025-05-28 11:49:01,955 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'long' data
2025-05-28 11:49:01,955 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant plant's own longitude coordinate decimal degrees GPS location'
2025-05-28 11:49:14,984 - src.simple_pipeline - INFO - Scraping 1/3: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 11:49:16,415 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-28 11:49:17,417 - src.simple_pipeline - INFO - Scraping 2/3: https://database.earth/countries/india/regions/haryana/cities/jhajjar
2025-05-28 11:49:19,504 - src.simple_pipeline - INFO - Successfully scraped https://database.earth/countries/india/regions/haryana/cities/jhajjar (685 chars)
2025-05-28 11:49:20,505 - src.simple_pipeline - INFO - Scraping 3/3: https://www.latlong.net/place/jhajjar-haryana-india-21959.html
2025-05-28 11:49:21,190 - src.simple_pipeline - INFO - Successfully scraped https://www.latlong.net/place/jhajjar-haryana-india-21959.html (1006 chars)
2025-05-28 11:49:22,193 - src.simple_pipeline - INFO - ✅ Found targeted content for 'long' - 3 sources
2025-05-28 11:49:22,194 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'long' using RAG: 76.6565
2025-05-28 11:49:22,194 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'plant_address' data
2025-05-28 11:49:22,194 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant district or city state country location'
2025-05-28 11:49:42,252 - src.simple_pipeline - INFO - Scraping 1/3: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 11:49:45,729 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-28 11:49:46,731 - src.simple_pipeline - INFO - Scraping 2/3: https://en.wikipedia.org/wiki/Jhajjar
2025-05-28 11:49:47,463 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar (6001 chars)
2025-05-28 11:49:48,465 - src.simple_pipeline - INFO - Scraping 3/3: https://apraava.com/projects/jhajjar-power-plant
2025-05-28 11:49:50,338 - src.simple_pipeline - INFO - Successfully scraped https://apraava.com/projects/jhajjar-power-plant (791 chars)
2025-05-28 11:49:51,339 - src.simple_pipeline - INFO - ✅ Found targeted content for 'plant_address' - 3 sources
2025-05-28 11:49:51,341 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'plant_address' using RAG: On The Road
2025-05-28 11:49:51,341 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'grid_connectivity_maps' data
2025-05-28 11:49:51,341 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant grid connection substation transmission line rated capacity classification voltage level'
2025-05-28 11:50:21,628 - src.serp_client - ERROR - Unexpected error in SERP API search: 
2025-05-28 11:50:31,655 - src.simple_pipeline - INFO - Scraping 1/3: https://herc.gov.in/writereaddata/pdf/stdtrn(3).pdf
2025-05-28 11:50:40,947 - src.pdf_processor - INFO - Successfully extracted 42630 chars from PDF using pdfplumber: https://herc.gov.in/writereaddata/pdf/stdtrn(3).pdf
2025-05-28 11:50:40,949 - src.scraper_client - INFO - Successfully processed PDF: https://herc.gov.in/writereaddata/pdf/stdtrn(3).pdf (42611 chars, score: 1.00)
2025-05-28 11:50:40,949 - src.simple_pipeline - INFO - Successfully scraped https://herc.gov.in/writereaddata/pdf/stdtrn(3).pdf (42611 chars)
2025-05-28 11:50:41,951 - src.simple_pipeline - INFO - Scraping 2/3: http://www.gajendrahaldea.in/pdf/Jhajjar-Transmission-MSS.pdf
2025-05-28 11:50:55,473 - src.pdf_processor - INFO - Successfully extracted 112384 chars from PDF using pdfplumber: http://www.gajendrahaldea.in/pdf/Jhajjar-Transmission-MSS.pdf
2025-05-28 11:50:55,476 - src.scraper_client - INFO - Successfully processed PDF: http://www.gajendrahaldea.in/pdf/Jhajjar-Transmission-MSS.pdf (50003 chars, score: 1.00)
2025-05-28 11:50:55,476 - src.simple_pipeline - INFO - Successfully scraped http://www.gajendrahaldea.in/pdf/Jhajjar-Transmission-MSS.pdf (50003 chars)
2025-05-28 11:50:56,478 - src.simple_pipeline - INFO - Scraping 3/3: https://www.scribd.com/document/337002790/Manual-of-Specs-Standards-for-Power-Transmission-Systems-india
2025-05-28 11:50:58,909 - readability.readability - INFO - ruthless removal did not work. 
2025-05-28 11:50:58,966 - src.simple_pipeline - INFO - Successfully scraped https://www.scribd.com/document/337002790/Manual-of-Specs-Standards-for-Power-Transmission-Systems-india (639 chars)
2025-05-28 11:50:59,968 - src.simple_pipeline - INFO - ✅ Found targeted content for 'grid_connectivity_maps' - 3 sources
2025-05-28 11:50:59,970 - src.simple_pipeline - INFO - 📝 Content found for 'grid_connectivity_maps' but RAG extraction failed
2025-05-28 11:50:59,970 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'ppa_details' data
2025-05-28 11:50:59,970 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant power purchase agreement PPA contract capacity commencement termination date entity procuring power'
2025-05-28 11:51:14,508 - src.simple_pipeline - INFO - Scraping 1/2: https://cercind.gov.in/2023/orders/637-MP-2020.pdf
2025-05-28 11:51:26,223 - src.pdf_processor - INFO - Successfully extracted 111606 chars from PDF using pdfplumber: https://cercind.gov.in/2023/orders/637-MP-2020.pdf
2025-05-28 11:51:26,225 - src.scraper_client - INFO - Successfully processed PDF: https://cercind.gov.in/2023/orders/637-MP-2020.pdf (50003 chars, score: 1.00)
2025-05-28 11:51:26,226 - src.simple_pipeline - INFO - Successfully scraped https://cercind.gov.in/2023/orders/637-MP-2020.pdf (50003 chars)
2025-05-28 11:51:27,227 - src.simple_pipeline - INFO - Scraping 2/2: https://www.eqmagpro.com/petition-of-the-power-purchase-agreement-ppa-for-approval-of-the-amendment-of-the-ppa-jhajjar-power-limited-jpl-eq-mag-pro/
2025-05-28 11:51:29,353 - src.simple_pipeline - WARNING - Content too short or failed for https://www.eqmagpro.com/petition-of-the-power-purchase-agreement-ppa-for-approval-of-the-amendment-of-the-ppa-jhajjar-power-limited-jpl-eq-mag-pro/
2025-05-28 11:51:30,355 - src.simple_pipeline - INFO - ✅ Found targeted content for 'ppa_details' - 1 sources
2025-05-28 11:51:30,356 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'ppa_details' using RAG: [{'description': 'Power Purchase Agreement for Jhajjar Power Plant', 'capacity': '1320 MW', 'capacity_unit': 'MW', 'start_date': '2012', 'end_date': '', 'tenure': 30, 'tenure_type': 'Years', 'respondents': [{'name': 'Haryana State Distribution Companies', 'capacity': '1188 MW', 'currency': 'INR', 'price': '', 'price_unit': 'INR/kWh'}, {'name': 'External Power Buyers', 'capacity': '132 MW', 'currency': 'INR', 'price': '', 'price_unit': 'INR/kWh'}]}]
2025-05-28 11:51:30,356 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'units_id' data
2025-05-28 11:51:30,356 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant integers from 1 to number of units at plant generation units'
2025-05-28 11:52:00,629 - src.serp_client - ERROR - Unexpected error in SERP API search: 
2025-05-28 11:52:12,813 - src.simple_pipeline - INFO - Scraping 1/3: https://www.apraava.com/getmedia/8932fe7b-f82d-4a82-ab8c-32843bd9de2e/2020-21.pdf
2025-05-28 11:52:23,984 - src.pdf_processor - INFO - Successfully extracted 89455 chars from PDF using pdfplumber: https://www.apraava.com/getmedia/8932fe7b-f82d-4a82-ab8c-32843bd9de2e/2020-21.pdf
2025-05-28 11:52:23,986 - src.scraper_client - INFO - Successfully processed PDF: https://www.apraava.com/getmedia/8932fe7b-f82d-4a82-ab8c-32843bd9de2e/2020-21.pdf (50003 chars, score: 1.00)
2025-05-28 11:52:23,986 - src.simple_pipeline - INFO - Successfully scraped https://www.apraava.com/getmedia/8932fe7b-f82d-4a82-ab8c-32843bd9de2e/2020-21.pdf (50003 chars)
2025-05-28 11:52:24,988 - src.simple_pipeline - INFO - Scraping 2/3: https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html
2025-05-28 11:52:36,473 - src.simple_pipeline - INFO - Successfully scraped https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html (516 chars)
2025-05-28 11:52:37,475 - src.simple_pipeline - INFO - Scraping 3/3: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 11:52:37,941 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-28 11:52:38,943 - src.simple_pipeline - INFO - ✅ Found targeted content for 'units_id' - 3 sources
2025-05-28 11:52:38,950 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'units_id' using RAG: ['Unit 1', 'Unit 2']
2025-05-28 11:52:38,955 - src.simple_pipeline - INFO - 💾 Targeted content saved to jhajjar_power_plant_targeted_content_20250528_115238.json
2025-05-28 11:52:38,957 - src.simple_pipeline - ERROR - Error creating PlantDetails object: 2 validation errors for PlantDetails
units_id.0
  Input should be a valid integer, unable to parse string as an integer [type=int_parsing, input_value='Unit 1', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/int_parsing
units_id.1
  Input should be a valid integer, unable to parse string as an integer [type=int_parsing, input_value='Unit 2', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/int_parsing
2025-05-28 11:52:38,957 - src.simple_pipeline - INFO - 🎉 Simplified extraction completed for: Jhajjar Power Plant
2025-05-28 11:52:38,957 - src.simple_pipeline - INFO - ⏱️  Total time: 267.6s
2025-05-28 11:52:38,957 - src.simple_pipeline - INFO - 💾 Cache efficiency: 3 fields from cache, 6 targeted searches
2025-05-28 11:52:38,958 - src.simple_pipeline - INFO - 📊 Organizational results saved to jhajjar_org_groq_rag_20250528_115238.json
2025-05-28 11:52:38,958 - src.simple_pipeline - ERROR - Error saving results: 'dict' object has no attribute 'model_dump'
2025-05-28 14:02:00,059 - src.enhanced_extractor - INFO - Enhanced data extractor initialized with Groq
2025-05-28 14:02:00,070 - src.plant_details_extractor - INFO - Plant details extractor initialized with Groq
2025-05-28 14:02:00,070 - src.simple_pipeline - INFO - 🔍 Starting simplified extraction for: Jhajjar Power Plant
2025-05-28 14:02:00,070 - src.simple_pipeline - INFO - 📡 Step 1: Searching for 'Jhajjar Power Plant' - getting top 5 links
2025-05-28 14:02:20,593 - src.simple_pipeline - INFO - 🌐 Step 2: Scraping 5 top results
2025-05-28 14:02:20,594 - src.simple_pipeline - INFO - Scraping 1/5: https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html
2025-05-28 14:02:24,457 - src.simple_pipeline - INFO - Successfully scraped https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html (516 chars)
2025-05-28 14:02:25,458 - src.simple_pipeline - INFO - Scraping 2/5: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 14:02:26,259 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-28 14:02:27,260 - src.simple_pipeline - INFO - Scraping 3/5: https://apraava.com/projects/jhajjar-power-plant
2025-05-28 14:02:29,695 - src.simple_pipeline - INFO - Successfully scraped https://apraava.com/projects/jhajjar-power-plant (791 chars)
2025-05-28 14:02:30,696 - src.simple_pipeline - INFO - Scraping 4/5: https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr
2025-05-28 14:02:36,733 - src.simple_pipeline - INFO - Successfully scraped https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr (3864 chars)
2025-05-28 14:02:37,735 - src.simple_pipeline - INFO - Scraping 5/5: https://m.economictimes.com/industry/energy/power/jindal-power-emerges-frontrunner-to-acquire-apraavas-haryana-project/articleshow/*********.cms
2025-05-28 14:02:40,096 - src.simple_pipeline - INFO - Successfully scraped https://m.economictimes.com/industry/energy/power/jindal-power-emerges-frontrunner-to-acquire-apraavas-haryana-project/articleshow/*********.cms (2246 chars)
2025-05-28 14:02:41,098 - src.simple_pipeline - INFO - ✅ Initial scraping completed: 5 pages scraped
2025-05-28 14:02:41,099 - src.simple_pipeline - INFO - 📊 Step 3: Extracting organizational details from scraped content
2025-05-28 14:02:41,099 - src.enhanced_extractor - INFO - Content analysis: {'total_sources': 5, 'total_content': 7907, 'avg_relevance': 0.8380000000000001, 'high_quality_sources': 1, 'source_type_diversity': 3, 'source_types': ['other', 'company_official', 'wikipedia']}
2025-05-28 14:02:41,099 - src.enhanced_extractor - INFO - Using basic extraction with validation
2025-05-28 14:02:41,099 - src.groq_client - INFO - Starting LLM extraction for 5 content pieces
2025-05-28 14:02:41,762 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 14:02:42,010 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 14:02:42,013 - src.groq_client - INFO - Extracted cfpp_type: joint_venture (confidence: 0.90)
2025-05-28 14:02:42,937 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 14:02:42,942 - src.groq_client - INFO - Extracted country_name: India (confidence: 0.80)
2025-05-28 14:02:43,910 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 14:02:43,911 - src.groq_client - INFO - Extracted province: Haryana (confidence: 0.70)
2025-05-28 14:02:44,951 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 14:02:44,955 - src.groq_client - INFO - Extracted plants_count: 1 (confidence: 0.80)
2025-05-28 14:02:45,821 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 14:02:45,825 - src.groq_client - INFO - Extracted plant_types: ['coal'] (confidence: 0.80)
2025-05-28 14:02:46,591 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 14:02:46,593 - src.groq_client - INFO - Extracted ppa_flag: None (confidence: 0.10)
2025-05-28 14:02:47,982 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 14:02:47,985 - src.groq_client - INFO - Extracted currency_in: None (confidence: 0.10)
2025-05-28 14:02:50,977 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 14:02:50,980 - src.groq_client - INFO - Extracted financial_year: 04-03 (confidence: 0.90)
2025-05-28 14:02:51,482 - src.groq_client - INFO - LLM extraction completed
2025-05-28 14:02:51,484 - src.simple_pipeline - INFO - ✅ Organizational extraction completed
2025-05-28 14:02:51,484 - src.simple_pipeline - INFO - 🔧 Step 4: Extracting plant details with smart field-by-field approach
2025-05-28 14:02:51,484 - src.simple_pipeline - INFO - 🧠 Analyzing cached content for plant details extraction
2025-05-28 14:02:51,484 - src.plant_details_extractor - INFO - Starting plant details extraction for: Jhajjar Power Plant
2025-05-28 14:02:51,484 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field name: 'NoneType' object has no attribute 'client'
2025-05-28 14:02:51,485 - src.plant_details_extractor - INFO - Extracted name: None (confidence: 0.00)
2025-05-28 14:02:51,986 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field plant_type: 'NoneType' object has no attribute 'client'
2025-05-28 14:02:51,986 - src.plant_details_extractor - INFO - Extracted plant_type: None (confidence: 0.00)
2025-05-28 14:02:52,488 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field plant_address: 'NoneType' object has no attribute 'client'
2025-05-28 14:02:52,488 - src.plant_details_extractor - INFO - Extracted plant_address: None (confidence: 0.00)
2025-05-28 14:02:52,990 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field lat: 'NoneType' object has no attribute 'client'
2025-05-28 14:02:52,991 - src.plant_details_extractor - INFO - Extracted lat: None (confidence: 0.00)
2025-05-28 14:02:53,493 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field long: 'NoneType' object has no attribute 'client'
2025-05-28 14:02:53,493 - src.plant_details_extractor - INFO - Extracted long: None (confidence: 0.00)
2025-05-28 14:02:53,995 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field units_id: 'NoneType' object has no attribute 'client'
2025-05-28 14:02:53,995 - src.plant_details_extractor - INFO - Extracted units_id: None (confidence: 0.00)
2025-05-28 14:02:54,497 - src.plant_details_extractor - ERROR - Failed to extract grid_connectivity: No prompt template found for plant field: grid_connectivity
2025-05-28 14:02:54,497 - src.plant_details_extractor - ERROR - Failed to extract ppa_details: '\n  "capacity"'
2025-05-28 14:02:54,498 - src.plant_details_extractor - INFO - Using organization name for plant name: CLP India Private Limited
2025-05-28 14:02:54,498 - src.plant_details_extractor - INFO - Derived plant_type from org data: coal
2025-05-28 14:02:54,498 - src.plant_details_extractor - INFO - Plant details extraction completed for: Jhajjar Power Plant
2025-05-28 14:02:54,498 - src.simple_pipeline - INFO - ✅ Field 'name' extracted from cache
2025-05-28 14:02:54,498 - src.simple_pipeline - INFO - ✅ Field 'plant_type' extracted from cache
2025-05-28 14:02:54,498 - src.simple_pipeline - INFO - ✅ Field 'plant_id' extracted from cache
2025-05-28 14:02:54,499 - src.simple_pipeline - INFO - 🔍 Field 'lat' is missing - will search specifically
2025-05-28 14:02:54,499 - src.simple_pipeline - INFO - 🔍 Field 'long' is missing - will search specifically
2025-05-28 14:02:54,499 - src.simple_pipeline - INFO - 🔍 Field 'plant_address' is missing - will search specifically
2025-05-28 14:02:54,499 - src.simple_pipeline - INFO - 🔍 Field 'grid_connectivity_maps' is missing - will search specifically
2025-05-28 14:02:54,499 - src.simple_pipeline - INFO - 🔍 Field 'ppa_details' is missing - will search specifically
2025-05-28 14:02:54,499 - src.simple_pipeline - INFO - 🔍 Field 'units_id' is missing - will search specifically
2025-05-28 14:02:54,499 - src.simple_pipeline - INFO - 🎯 Starting targeted searches for 6 missing fields
2025-05-28 14:02:54,499 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'lat' data
2025-05-28 14:02:54,500 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant plant's own latitude coordinate decimal degrees GPS location'
2025-05-28 14:03:46,052 - src.simple_pipeline - INFO - Scraping 1/3: https://database.earth/countries/india/regions/haryana/cities/jhajjar
2025-05-28 14:03:47,742 - src.simple_pipeline - INFO - Successfully scraped https://database.earth/countries/india/regions/haryana/cities/jhajjar (685 chars)
2025-05-28 14:03:48,744 - src.simple_pipeline - INFO - Scraping 2/3: https://www.latlong.net/place/jhajjar-haryana-india-21959.html
2025-05-28 14:03:53,605 - src.simple_pipeline - INFO - Successfully scraped https://www.latlong.net/place/jhajjar-haryana-india-21959.html (1006 chars)
2025-05-28 14:03:54,607 - src.simple_pipeline - INFO - Scraping 3/3: https://www.gps-coordinates.net/
2025-05-28 14:03:55,151 - src.simple_pipeline - INFO - Successfully scraped https://www.gps-coordinates.net/ (1836 chars)
2025-05-28 14:03:56,153 - src.simple_pipeline - INFO - ✅ Found targeted content for 'lat' - 3 sources
2025-05-28 14:03:56,154 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'lat' using RAG: 28.607111
2025-05-28 14:03:56,155 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'long' data
2025-05-28 14:03:56,155 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant plant's own longitude coordinate decimal degrees GPS location'
2025-05-28 14:04:20,181 - src.simple_pipeline - INFO - Scraping 1/3: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 14:04:23,106 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-28 14:04:24,108 - src.simple_pipeline - INFO - Scraping 2/3: https://database.earth/countries/india/regions/haryana/cities/jhajjar
2025-05-28 14:04:24,751 - src.simple_pipeline - INFO - Successfully scraped https://database.earth/countries/india/regions/haryana/cities/jhajjar (685 chars)
2025-05-28 14:04:25,752 - src.simple_pipeline - INFO - Scraping 3/3: https://www.latlong.net/place/jhajjar-haryana-india-21959.html
2025-05-28 14:04:26,935 - src.simple_pipeline - INFO - Successfully scraped https://www.latlong.net/place/jhajjar-haryana-india-21959.html (1006 chars)
2025-05-28 14:04:27,937 - src.simple_pipeline - INFO - ✅ Found targeted content for 'long' - 3 sources
2025-05-28 14:04:27,939 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'long' using RAG: 76.6565
2025-05-28 14:04:27,939 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'plant_address' data
2025-05-28 14:04:27,939 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant district or city state country location'
2025-05-28 14:04:39,410 - src.simple_pipeline - INFO - Scraping 1/3: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 14:04:40,989 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-28 14:04:41,991 - src.simple_pipeline - INFO - Scraping 2/3: https://en.wikipedia.org/wiki/Jhajjar
2025-05-28 14:04:42,847 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar (6001 chars)
2025-05-28 14:04:43,848 - src.simple_pipeline - INFO - Scraping 3/3: https://apraava.com/projects/jhajjar-power-plant
2025-05-28 14:04:45,927 - src.simple_pipeline - INFO - Successfully scraped https://apraava.com/projects/jhajjar-power-plant (791 chars)
2025-05-28 14:04:46,929 - src.simple_pipeline - INFO - ✅ Found targeted content for 'plant_address' - 3 sources
2025-05-28 14:04:46,931 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'plant_address' using RAG: On The Road
2025-05-28 14:04:46,932 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'grid_connectivity_maps' data
2025-05-28 14:04:46,932 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant grid connection substation transmission line rated capacity classification voltage level'
2025-05-28 14:05:20,978 - src.simple_pipeline - INFO - Scraping 1/3: https://herc.gov.in/writereaddata/pdf/stdtrn(3).pdf
2025-05-28 14:05:33,523 - src.pdf_processor - INFO - Successfully extracted 42630 chars from PDF using pdfplumber: https://herc.gov.in/writereaddata/pdf/stdtrn(3).pdf
2025-05-28 14:05:33,525 - src.scraper_client - INFO - Successfully processed PDF: https://herc.gov.in/writereaddata/pdf/stdtrn(3).pdf (42611 chars, score: 1.00)
2025-05-28 14:05:33,525 - src.simple_pipeline - INFO - Successfully scraped https://herc.gov.in/writereaddata/pdf/stdtrn(3).pdf (42611 chars)
2025-05-28 14:05:34,526 - src.simple_pipeline - INFO - Scraping 2/3: http://www.gajendrahaldea.in/pdf/Jhajjar-Transmission-MSS.pdf
2025-05-28 14:06:08,438 - src.pdf_processor - INFO - Successfully extracted 112384 chars from PDF using pdfplumber: http://www.gajendrahaldea.in/pdf/Jhajjar-Transmission-MSS.pdf
2025-05-28 14:06:08,441 - src.scraper_client - INFO - Successfully processed PDF: http://www.gajendrahaldea.in/pdf/Jhajjar-Transmission-MSS.pdf (50003 chars, score: 1.00)
2025-05-28 14:06:08,441 - src.simple_pipeline - INFO - Successfully scraped http://www.gajendrahaldea.in/pdf/Jhajjar-Transmission-MSS.pdf (50003 chars)
2025-05-28 14:06:09,443 - src.simple_pipeline - INFO - Scraping 3/3: https://www.scribd.com/document/337002790/Manual-of-Specs-Standards-for-Power-Transmission-Systems-india
2025-05-28 14:06:16,680 - readability.readability - INFO - ruthless removal did not work. 
2025-05-28 14:06:16,735 - src.simple_pipeline - INFO - Successfully scraped https://www.scribd.com/document/337002790/Manual-of-Specs-Standards-for-Power-Transmission-Systems-india (639 chars)
2025-05-28 14:06:17,737 - src.simple_pipeline - INFO - ✅ Found targeted content for 'grid_connectivity_maps' - 3 sources
2025-05-28 14:06:17,739 - src.simple_pipeline - INFO - 📝 Content found for 'grid_connectivity_maps' but RAG extraction failed
2025-05-28 14:06:17,739 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'ppa_details' data
2025-05-28 14:06:17,739 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant power purchase agreement PPA contract capacity commencement termination date entity procuring power'
2025-05-28 14:06:26,849 - src.simple_pipeline - INFO - Scraping 1/2: https://cercind.gov.in/2023/orders/637-MP-2020.pdf
2025-05-28 14:06:42,112 - src.pdf_processor - INFO - Successfully extracted 111606 chars from PDF using pdfplumber: https://cercind.gov.in/2023/orders/637-MP-2020.pdf
2025-05-28 14:06:42,115 - src.scraper_client - INFO - Successfully processed PDF: https://cercind.gov.in/2023/orders/637-MP-2020.pdf (50003 chars, score: 1.00)
2025-05-28 14:06:42,115 - src.simple_pipeline - INFO - Successfully scraped https://cercind.gov.in/2023/orders/637-MP-2020.pdf (50003 chars)
2025-05-28 14:06:43,116 - src.simple_pipeline - INFO - Scraping 2/2: https://www.eqmagpro.com/petition-of-the-power-purchase-agreement-ppa-for-approval-of-the-amendment-of-the-ppa-jhajjar-power-limited-jpl-eq-mag-pro/
2025-05-28 14:06:52,417 - src.simple_pipeline - WARNING - Content too short or failed for https://www.eqmagpro.com/petition-of-the-power-purchase-agreement-ppa-for-approval-of-the-amendment-of-the-ppa-jhajjar-power-limited-jpl-eq-mag-pro/
2025-05-28 14:06:53,419 - src.simple_pipeline - INFO - ✅ Found targeted content for 'ppa_details' - 1 sources
2025-05-28 14:06:53,419 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'ppa_details' using RAG: [{'description': 'Power Purchase Agreement for Jhajjar Power Plant', 'capacity': '1320 MW', 'capacity_unit': 'MW', 'start_date': '2012', 'end_date': '', 'tenure': 30, 'tenure_type': 'Years', 'respondents': [{'name': 'Haryana State Distribution Companies', 'capacity': '1188 MW', 'currency': 'INR', 'price': '', 'price_unit': 'INR/kWh'}, {'name': 'External Power Buyers', 'capacity': '132 MW', 'currency': 'INR', 'price': '', 'price_unit': 'INR/kWh'}]}]
2025-05-28 14:06:53,420 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'units_id' data
2025-05-28 14:06:53,420 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant integers from 1 to number of units at plant generation units'
2025-05-28 14:07:49,211 - src.serp_client - ERROR - SERP API request failed: 500, message='Internal Server Error', url=URL('https://api.scraperapi.com/structured/google/search?api_key=********************************&query=Jhajjar+Power+Plant+integers+from+1+to+number+of+units+at+plant+generation+units&num=3&page=1')
2025-05-28 14:08:34,322 - src.simple_pipeline - INFO - Scraping 1/3: https://www.apraava.com/getmedia/8932fe7b-f82d-4a82-ab8c-32843bd9de2e/2020-21.pdf
2025-05-28 14:08:55,492 - src.pdf_processor - INFO - Successfully extracted 89455 chars from PDF using pdfplumber: https://www.apraava.com/getmedia/8932fe7b-f82d-4a82-ab8c-32843bd9de2e/2020-21.pdf
2025-05-28 14:08:55,495 - src.scraper_client - INFO - Successfully processed PDF: https://www.apraava.com/getmedia/8932fe7b-f82d-4a82-ab8c-32843bd9de2e/2020-21.pdf (50003 chars, score: 1.00)
2025-05-28 14:08:55,495 - src.simple_pipeline - INFO - Successfully scraped https://www.apraava.com/getmedia/8932fe7b-f82d-4a82-ab8c-32843bd9de2e/2020-21.pdf (50003 chars)
2025-05-28 14:08:56,496 - src.simple_pipeline - INFO - Scraping 2/3: https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html
2025-05-28 14:08:58,985 - src.simple_pipeline - INFO - Successfully scraped https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html (516 chars)
2025-05-28 14:08:59,986 - src.simple_pipeline - INFO - Scraping 3/3: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 14:09:04,353 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-28 14:09:05,355 - src.simple_pipeline - INFO - ✅ Found targeted content for 'units_id' - 3 sources
2025-05-28 14:09:05,365 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'units_id' using RAG: [1, 2]
2025-05-28 14:09:05,371 - src.simple_pipeline - INFO - 💾 Targeted content saved to jhajjar_power_plant_targeted_content_20250528_140905.json
2025-05-28 14:09:05,372 - src.simple_pipeline - INFO - 🎉 Simplified extraction completed for: Jhajjar Power Plant
2025-05-28 14:09:05,372 - src.simple_pipeline - INFO - ⏱️  Total time: 425.3s
2025-05-28 14:09:05,372 - src.simple_pipeline - INFO - 💾 Cache efficiency: 3 fields from cache, 6 targeted searches
2025-05-28 14:09:05,373 - src.simple_pipeline - INFO - 📊 Organizational results saved to jhajjar_org_groq_rag_20250528_140905.json
2025-05-28 14:09:05,374 - src.simple_pipeline - INFO - 🔧 Plant details results saved to jhajjar_plant_groq_rag_20250528_140905.json
2025-05-28 14:09:05,374 - src.simple_pipeline - INFO - 📈 Extraction info saved to jhajjar_extraction_info_groq_rag_20250528_140905.json
2025-05-28 15:25:26,750 - src.enhanced_extractor - INFO - Enhanced data extractor initialized with Groq
2025-05-28 15:25:26,762 - src.plant_details_extractor - INFO - Plant details extractor initialized with Groq
2025-05-28 15:25:26,762 - src.simple_pipeline - INFO - 🔍 Starting simplified extraction for: Jhajjar Power Plant
2025-05-28 15:25:26,762 - src.simple_pipeline - INFO - 📡 Step 1: Searching for 'Jhajjar Power Plant' - getting top 5 links
2025-05-28 15:25:36,712 - src.simple_pipeline - INFO - 🌐 Step 2: Scraping 5 top results
2025-05-28 15:25:36,712 - src.simple_pipeline - INFO - Scraping 1/5: https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html
2025-05-28 15:25:37,568 - src.simple_pipeline - INFO - Successfully scraped https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html (516 chars)
2025-05-28 15:25:38,569 - src.simple_pipeline - INFO - Scraping 2/5: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 15:25:39,246 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-28 15:25:40,248 - src.simple_pipeline - INFO - Scraping 3/5: https://apraava.com/projects/jhajjar-power-plant
2025-05-28 15:25:42,258 - src.simple_pipeline - INFO - Successfully scraped https://apraava.com/projects/jhajjar-power-plant (791 chars)
2025-05-28 15:25:43,260 - src.simple_pipeline - INFO - Scraping 4/5: https://m.economictimes.com/industry/energy/power/jindal-power-emerges-frontrunner-to-acquire-apraavas-haryana-project/articleshow/*********.cms
2025-05-28 15:25:46,145 - src.simple_pipeline - INFO - Successfully scraped https://m.economictimes.com/industry/energy/power/jindal-power-emerges-frontrunner-to-acquire-apraavas-haryana-project/articleshow/*********.cms (2246 chars)
2025-05-28 15:25:47,146 - src.simple_pipeline - INFO - Scraping 5/5: https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr
2025-05-28 15:25:48,837 - src.simple_pipeline - INFO - Successfully scraped https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr (3864 chars)
2025-05-28 15:25:49,840 - src.simple_pipeline - INFO - ✅ Initial scraping completed: 5 pages scraped
2025-05-28 15:25:49,842 - src.simple_pipeline - INFO - 📊 Step 3: Extracting organizational details from scraped content
2025-05-28 15:25:49,842 - src.enhanced_extractor - INFO - Content analysis: {'total_sources': 5, 'total_content': 7907, 'avg_relevance': 0.8380000000000001, 'high_quality_sources': 1, 'source_type_diversity': 3, 'source_types': ['other', 'wikipedia', 'company_official']}
2025-05-28 15:25:49,842 - src.enhanced_extractor - INFO - Using basic extraction with validation
2025-05-28 15:25:49,842 - src.groq_client - INFO - Starting LLM extraction for 5 content pieces
2025-05-28 15:25:50,339 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 15:25:50,640 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 15:25:50,643 - src.groq_client - INFO - Extracted cfpp_type: joint_venture (confidence: 0.90)
2025-05-28 15:25:51,482 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 15:25:51,484 - src.groq_client - INFO - Extracted country_name: India (confidence: 0.80)
2025-05-28 15:25:52,217 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 15:25:52,220 - src.groq_client - INFO - Extracted province: Haryana (confidence: 0.70)
2025-05-28 15:25:52,958 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 15:25:52,961 - src.groq_client - INFO - Extracted plants_count: 1 (confidence: 0.80)
2025-05-28 15:25:53,931 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 15:25:53,935 - src.groq_client - INFO - Extracted plant_types: ['coal', 'gas'] (confidence: 0.80)
2025-05-28 15:25:55,014 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 15:25:55,018 - src.groq_client - INFO - Extracted ppa_flag: Plant (confidence: 0.80)
2025-05-28 15:25:55,766 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 15:25:55,769 - src.groq_client - INFO - Extracted currency_in: INR (confidence: 0.90)
2025-05-28 15:25:56,518 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-28 15:25:56,521 - src.groq_client - INFO - Extracted financial_year: 04-03 (confidence: 0.90)
2025-05-28 15:25:57,022 - src.groq_client - INFO - LLM extraction completed
2025-05-28 15:25:57,024 - src.simple_pipeline - INFO - ✅ Organizational extraction completed
2025-05-28 15:25:57,024 - src.simple_pipeline - INFO - 🔧 Step 4: Extracting plant details with smart field-by-field approach
2025-05-28 15:25:57,024 - src.simple_pipeline - INFO - 🧠 Analyzing cached content for plant details extraction
2025-05-28 15:25:57,024 - src.plant_details_extractor - INFO - Starting plant details extraction for: Jhajjar Power Plant
2025-05-28 15:25:57,024 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field name: 'NoneType' object has no attribute 'client'
2025-05-28 15:25:57,024 - src.plant_details_extractor - INFO - Extracted name: None (confidence: 0.00)
2025-05-28 15:25:57,526 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field plant_type: 'NoneType' object has no attribute 'client'
2025-05-28 15:25:57,527 - src.plant_details_extractor - INFO - Extracted plant_type: None (confidence: 0.00)
2025-05-28 15:25:58,029 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field plant_address: 'NoneType' object has no attribute 'client'
2025-05-28 15:25:58,029 - src.plant_details_extractor - INFO - Extracted plant_address: None (confidence: 0.00)
2025-05-28 15:25:58,530 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field lat: 'NoneType' object has no attribute 'client'
2025-05-28 15:25:58,531 - src.plant_details_extractor - INFO - Extracted lat: None (confidence: 0.00)
2025-05-28 15:25:59,032 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field long: 'NoneType' object has no attribute 'client'
2025-05-28 15:25:59,033 - src.plant_details_extractor - INFO - Extracted long: None (confidence: 0.00)
2025-05-28 15:25:59,535 - src.plant_details_extractor - ERROR - LLM extraction failed for plant field units_id: 'NoneType' object has no attribute 'client'
2025-05-28 15:25:59,535 - src.plant_details_extractor - INFO - Extracted units_id: None (confidence: 0.00)
2025-05-28 15:26:00,037 - src.plant_details_extractor - ERROR - Failed to extract grid_connectivity: No prompt template found for plant field: grid_connectivity
2025-05-28 15:26:00,037 - src.plant_details_extractor - ERROR - Failed to extract ppa_details: '\n  "capacity"'
2025-05-28 15:26:00,038 - src.plant_details_extractor - INFO - Using organization name for plant name: CLP India Private Limited
2025-05-28 15:26:00,038 - src.plant_details_extractor - INFO - Derived plant_type from org data: coal
2025-05-28 15:26:00,038 - src.plant_details_extractor - INFO - Plant details extraction completed for: Jhajjar Power Plant
2025-05-28 15:26:00,038 - src.simple_pipeline - INFO - ✅ Field 'name' extracted from cache
2025-05-28 15:26:00,038 - src.simple_pipeline - INFO - ✅ Field 'plant_type' extracted from cache
2025-05-28 15:26:00,038 - src.simple_pipeline - INFO - ✅ Field 'plant_id' extracted from cache
2025-05-28 15:26:00,038 - src.simple_pipeline - INFO - 🔍 Field 'lat' is missing - will search specifically
2025-05-28 15:26:00,039 - src.simple_pipeline - INFO - 🔍 Field 'long' is missing - will search specifically
2025-05-28 15:26:00,039 - src.simple_pipeline - INFO - 🔍 Field 'plant_address' is missing - will search specifically
2025-05-28 15:26:00,039 - src.simple_pipeline - INFO - 🔍 Field 'grid_connectivity_maps' is missing - will search specifically
2025-05-28 15:26:00,039 - src.simple_pipeline - INFO - 🔍 Field 'ppa_details' is missing - will search specifically
2025-05-28 15:26:00,039 - src.simple_pipeline - INFO - 🔍 Field 'units_id' is missing - will search specifically
2025-05-28 15:26:00,039 - src.simple_pipeline - INFO - 🎯 Starting targeted searches for 6 missing fields
2025-05-28 15:26:00,039 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'lat' data
2025-05-28 15:26:00,039 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant latitude coordinates GPS location'
2025-05-28 15:26:03,890 - src.simple_pipeline - INFO - Scraping 1/3: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 15:26:09,506 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-28 15:26:10,508 - src.simple_pipeline - INFO - Scraping 2/3: https://database.earth/countries/india/regions/haryana/cities/jhajjar
2025-05-28 15:26:11,249 - src.simple_pipeline - INFO - Successfully scraped https://database.earth/countries/india/regions/haryana/cities/jhajjar (685 chars)
2025-05-28 15:26:12,251 - src.simple_pipeline - INFO - Scraping 3/3: https://www.latlong.net/place/jhajjar-haryana-india-21959.html
2025-05-28 15:26:13,977 - src.simple_pipeline - INFO - Successfully scraped https://www.latlong.net/place/jhajjar-haryana-india-21959.html (1006 chars)
2025-05-28 15:26:14,979 - src.simple_pipeline - INFO - ✅ Found targeted content for 'lat' - 3 sources
2025-05-28 15:26:14,982 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'lat' using RAG: 28.607111
2025-05-28 15:26:14,982 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'long' data
2025-05-28 15:26:14,982 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant longitude coordinates GPS location'
2025-05-28 15:26:37,192 - src.simple_pipeline - INFO - Scraping 1/3: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 15:26:39,839 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-28 15:26:40,841 - src.simple_pipeline - INFO - Scraping 2/3: https://database.earth/countries/india/regions/haryana/cities/jhajjar
2025-05-28 15:26:41,995 - src.simple_pipeline - INFO - Successfully scraped https://database.earth/countries/india/regions/haryana/cities/jhajjar (685 chars)
2025-05-28 15:26:42,996 - src.simple_pipeline - INFO - Scraping 3/3: https://www.latlong.net/place/jhajjar-haryana-india-21959.html
2025-05-28 15:26:44,006 - src.simple_pipeline - INFO - Successfully scraped https://www.latlong.net/place/jhajjar-haryana-india-21959.html (1006 chars)
2025-05-28 15:26:45,007 - src.simple_pipeline - INFO - ✅ Found targeted content for 'long' - 3 sources
2025-05-28 15:26:45,009 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'long' using RAG: 76.6565
2025-05-28 15:26:45,009 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'plant_address' data
2025-05-28 15:26:45,009 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant complete address location street city'
2025-05-28 15:26:53,178 - src.simple_pipeline - INFO - Scraping 1/2: https://www.bloomberg.com/profile/company/0808736D:IN
2025-05-28 15:26:59,302 - src.simple_pipeline - INFO - Successfully scraped https://www.bloomberg.com/profile/company/0808736D:IN (193 chars)
2025-05-28 15:27:00,303 - src.simple_pipeline - INFO - Scraping 2/2: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 15:27:06,290 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-28 15:27:07,293 - src.simple_pipeline - INFO - ✅ Found targeted content for 'plant_address' - 2 sources
2025-05-28 15:27:07,296 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'plant_address' using RAG: Jharli village, Jhajjar district, Haryana, India
2025-05-28 15:27:07,296 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'grid_connectivity_maps' data
2025-05-28 15:27:07,296 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant grid connection substation transmission line'
2025-05-28 15:27:10,769 - src.simple_pipeline - INFO - Scraping 1/3: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf
2025-05-28 15:27:19,514 - src.pdf_processor - INFO - Successfully extracted 132704 chars from PDF using pdfplumber: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf
2025-05-28 15:27:19,517 - src.scraper_client - INFO - Successfully processed PDF: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf (50003 chars, score: 1.00)
2025-05-28 15:27:19,517 - src.simple_pipeline - INFO - Successfully scraped https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf (50003 chars)
2025-05-28 15:27:20,519 - src.simple_pipeline - INFO - Scraping 2/3: http://www.gajendrahaldea.in/pdf/Jhajjar-Transmission-MSS.pdf
2025-05-28 15:27:57,822 - src.pdf_processor - INFO - Successfully extracted 112384 chars from PDF using pdfplumber: http://www.gajendrahaldea.in/pdf/Jhajjar-Transmission-MSS.pdf
2025-05-28 15:27:57,825 - src.scraper_client - INFO - Successfully processed PDF: http://www.gajendrahaldea.in/pdf/Jhajjar-Transmission-MSS.pdf (50003 chars, score: 1.00)
2025-05-28 15:27:57,826 - src.simple_pipeline - INFO - Successfully scraped http://www.gajendrahaldea.in/pdf/Jhajjar-Transmission-MSS.pdf (50003 chars)
2025-05-28 15:27:58,827 - src.simple_pipeline - INFO - Scraping 3/3: https://www.indigrid.co.in/portfolio-assets/jhajjar-kt-transco-pvt-ltd-jktpl/
2025-05-28 15:28:01,364 - src.simple_pipeline - INFO - Successfully scraped https://www.indigrid.co.in/portfolio-assets/jhajjar-kt-transco-pvt-ltd-jktpl/ (846 chars)
2025-05-28 15:28:02,366 - src.simple_pipeline - INFO - ✅ Found targeted content for 'grid_connectivity_maps' - 3 sources
2025-05-28 15:28:02,367 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'grid_connectivity_maps' using RAG: [{'description': 'Grid connectivity details for Jhajjar Power Plant', 'details': [{'substation_name': 'Sonipat Substation', 'substation_type': 'transmission', 'capacity': '400 kV', 'latitude': '', 'longitude': '', 'description': 'Primary transmission connection point - approximately 70 km northeast', 'projects': [{'description': '400 kV transmission line from Jhajjar to Sonipat', 'distance': '70 km'}]}, {'substation_name': 'Mahendragarh Substation', 'substation_type': 'transmission', 'capacity': '400 kV', 'latitude': '', 'longitude': '', 'description': 'Secondary transmission connection point - approximately 50 km southwest', 'projects': [{'description': '400 kV transmission line from Jhajjar to Mahendragarh', 'distance': '50 km'}]}]}]
2025-05-28 15:28:02,367 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'ppa_details' data
2025-05-28 15:28:02,368 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant power purchase agreement PPA contract details'
2025-05-28 15:28:05,195 - src.simple_pipeline - INFO - Scraping 1/3: https://www.derc.gov.in/sites/default/files/NDPL%20-%20Jhajjar%20Power%20Ltd..pdf
2025-05-28 15:28:11,417 - src.pdf_processor - INFO - Successfully extracted 36539 chars from PDF using pdfplumber: https://www.derc.gov.in/sites/default/files/NDPL%20-%20Jhajjar%20Power%20Ltd..pdf
2025-05-28 15:28:11,419 - src.scraper_client - INFO - Successfully processed PDF: https://www.derc.gov.in/sites/default/files/NDPL%20-%20Jhajjar%20Power%20Ltd..pdf (36524 chars, score: 1.00)
2025-05-28 15:28:11,419 - src.simple_pipeline - INFO - Successfully scraped https://www.derc.gov.in/sites/default/files/NDPL%20-%20Jhajjar%20Power%20Ltd..pdf (36524 chars)
2025-05-28 15:28:12,420 - src.simple_pipeline - INFO - Scraping 2/3: https://cercind.gov.in/2023/orders/637-MP-2020.pdf
2025-05-28 15:28:19,090 - src.pdf_processor - INFO - Successfully extracted 111606 chars from PDF using pdfplumber: https://cercind.gov.in/2023/orders/637-MP-2020.pdf
2025-05-28 15:28:19,092 - src.scraper_client - INFO - Successfully processed PDF: https://cercind.gov.in/2023/orders/637-MP-2020.pdf (50003 chars, score: 1.00)
2025-05-28 15:28:19,092 - src.simple_pipeline - INFO - Successfully scraped https://cercind.gov.in/2023/orders/637-MP-2020.pdf (50003 chars)
2025-05-28 15:28:20,093 - src.simple_pipeline - INFO - Scraping 3/3: https://www.eqmagpro.com/petition-of-the-power-purchase-agreement-ppa-for-approval-of-the-amendment-of-the-ppa-jhajjar-power-limited-jpl-eq-mag-pro/
2025-05-28 15:28:23,335 - src.simple_pipeline - WARNING - Content too short or failed for https://www.eqmagpro.com/petition-of-the-power-purchase-agreement-ppa-for-approval-of-the-amendment-of-the-ppa-jhajjar-power-limited-jpl-eq-mag-pro/
2025-05-28 15:28:24,337 - src.simple_pipeline - INFO - ✅ Found targeted content for 'ppa_details' - 2 sources
2025-05-28 15:28:24,339 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'ppa_details' using RAG: [{'description': 'Power Purchase Agreement for Jhajjar Power Plant', 'capacity': '1320 MW', 'capacity_unit': 'MW', 'start_date': '2012', 'end_date': '', 'tenure': 30, 'tenure_type': 'Years', 'respondents': [{'name': 'Haryana State Distribution Companies', 'capacity': '1188 MW', 'currency': 'INR', 'price': '', 'price_unit': 'INR/kWh'}, {'name': 'External Power Buyers', 'capacity': '132 MW', 'currency': 'INR', 'price': '', 'price_unit': 'INR/kWh'}]}]
2025-05-28 15:28:24,339 - src.simple_pipeline - INFO - 🎯 Searching specifically for 'units_id' data
2025-05-28 15:28:24,339 - src.simple_pipeline - INFO - 🔍 Targeted search query: 'Jhajjar Power Plant units generators unit numbers'
2025-05-28 15:28:26,642 - src.simple_pipeline - INFO - Scraping 1/3: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf
2025-05-28 15:28:32,312 - src.pdf_processor - INFO - Successfully extracted 132704 chars from PDF using pdfplumber: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf
2025-05-28 15:28:32,314 - src.scraper_client - INFO - Successfully processed PDF: https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf (50003 chars, score: 1.00)
2025-05-28 15:28:32,314 - src.simple_pipeline - INFO - Successfully scraped https://www.adb.org/sites/default/files/project-documents//42933-ind-seia.pdf (50003 chars)
2025-05-28 15:28:33,316 - src.simple_pipeline - INFO - Scraping 2/3: https://en.wikipedia.org/wiki/Jhajjar_Power_Station
2025-05-28 15:28:34,385 - src.simple_pipeline - INFO - Successfully scraped https://en.wikipedia.org/wiki/Jhajjar_Power_Station (490 chars)
2025-05-28 15:28:35,386 - src.simple_pipeline - INFO - Scraping 3/3: https://carbonmarketwatch.org/wp-content/uploads/2012/10/comments_on_jhajjar_haryana_project.pdf
2025-05-28 15:28:40,301 - src.pdf_processor - INFO - Successfully extracted 67258 chars from PDF using pdfplumber: https://carbonmarketwatch.org/wp-content/uploads/2012/10/comments_on_jhajjar_haryana_project.pdf
2025-05-28 15:28:40,302 - src.scraper_client - INFO - Successfully processed PDF: https://carbonmarketwatch.org/wp-content/uploads/2012/10/comments_on_jhajjar_haryana_project.pdf (50003 chars, score: 1.00)
2025-05-28 15:28:40,303 - src.simple_pipeline - INFO - Successfully scraped https://carbonmarketwatch.org/wp-content/uploads/2012/10/comments_on_jhajjar_haryana_project.pdf (50003 chars)
2025-05-28 15:28:41,305 - src.simple_pipeline - INFO - ✅ Found targeted content for 'units_id' - 3 sources
2025-05-28 15:28:41,315 - src.simple_pipeline - INFO - 🎉 Successfully extracted 'units_id' using RAG: ['Unit 1', 'Unit 3', 'Unit 5', 'Unit 2', 'Unit 6', 'Unit 4', 'Unit 250']
2025-05-28 15:28:41,321 - src.simple_pipeline - INFO - 💾 Targeted content saved to jhajjar_power_plant_targeted_content_20250528_152841.json
2025-05-28 15:28:41,322 - src.simple_pipeline - ERROR - Error creating PlantDetails object: 7 validation errors for PlantDetails
units_id.0
  Input should be a valid integer, unable to parse string as an integer [type=int_parsing, input_value='Unit 1', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/int_parsing
units_id.1
  Input should be a valid integer, unable to parse string as an integer [type=int_parsing, input_value='Unit 3', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/int_parsing
units_id.2
  Input should be a valid integer, unable to parse string as an integer [type=int_parsing, input_value='Unit 5', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/int_parsing
units_id.3
  Input should be a valid integer, unable to parse string as an integer [type=int_parsing, input_value='Unit 2', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/int_parsing
units_id.4
  Input should be a valid integer, unable to parse string as an integer [type=int_parsing, input_value='Unit 6', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/int_parsing
units_id.5
  Input should be a valid integer, unable to parse string as an integer [type=int_parsing, input_value='Unit 4', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/int_parsing
units_id.6
  Input should be a valid integer, unable to parse string as an integer [type=int_parsing, input_value='Unit 250', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/int_parsing
2025-05-28 15:28:41,323 - src.simple_pipeline - INFO - 🎉 Simplified extraction completed for: Jhajjar Power Plant
2025-05-28 15:28:41,323 - src.simple_pipeline - INFO - ⏱️  Total time: 194.6s
2025-05-28 15:28:41,323 - src.simple_pipeline - INFO - 💾 Cache efficiency: 3 fields from cache, 6 targeted searches
2025-05-28 15:28:41,324 - src.simple_pipeline - INFO - 📊 Organizational results saved to jhajjar_org_groq_rag_20250528_152841.json
2025-05-28 15:28:41,324 - src.simple_pipeline - ERROR - Error saving results: 'dict' object has no attribute 'model_dump'
